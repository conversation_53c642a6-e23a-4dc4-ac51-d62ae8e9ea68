# Android APP Analytics - 项目结构

```
Android APP Analytics/
├── README.md                          # 项目说明文档
├── requirements.txt                   # Python依赖包列表
├── setup.py                          # 项目安装配置
├── config/
│   ├── __init__.py
│   ├── settings.py                    # 全局配置文件
│   └── logging_config.py              # 日志配置
├── src/
│   ├── __init__.py
│   ├── main.py                        # 主程序入口
│   ├── core/
│   │   ├── __init__.py
│   │   ├── apk_parser.py              # APK文件验证和解包
│   │   ├── feature_extractor.py       # 特征提取主控制器
│   │   ├── manifest_analyzer.py       # AndroidManifest.xml解析
│   │   ├── code_analyzer.py           # 代码分析（Androguard + Jadx）
│   │   ├── resource_analyzer.py       # 资源文件分析
│   │   ├── native_analyzer.py         # 原生库分析
│   │   └── network_analyzer.py        # 网络特征提取
│   ├── database/
│   │   ├── __init__.py
│   │   ├── database_manager.py        # 数据库管理器
│   │   ├── models.py                  # 数据模型定义
│   │   └── schema.sql                 # 数据库建表语句
│   ├── analysis/
│   │   ├── __init__.py
│   │   ├── risk_calculator.py         # 风险评分算法
│   │   ├── similarity_matcher.py      # 相似度匹配算法
│   │   └── pattern_detector.py        # 模式检测器
│   ├── reporting/
│   │   ├── __init__.py
│   │   ├── report_generator.py        # 报告生成器
│   │   ├── json_formatter.py          # JSON报告格式化
│   │   └── html_formatter.py          # HTML报告格式化
│   └── utils/
│       ├── __init__.py
│       ├── file_utils.py              # 文件操作工具
│       ├── hash_utils.py              # 哈希计算工具
│       ├── external_tools.py          # 外部工具调用（Jadx, Apktool）
│       └── validators.py              # 数据验证工具
├── templates/
│   ├── report_template.html           # HTML报告模板
│   ├── css/
│   │   └── report_styles.css          # 报告样式
│   └── js/
│       └── report_interactive.js      # 报告交互功能
├── data/
│   ├── database/
│   │   └── features.db                # SQLite特征数据库
│   ├── signatures/
│   │   ├── malware_signatures.json    # 恶意软件特征库
│   │   └── known_certificates.json    # 已知证书库
│   └── temp/                          # 临时文件目录
├── tests/
│   ├── __init__.py
│   ├── test_apk_parser.py
│   ├── test_feature_extractor.py
│   ├── test_database_manager.py
│   ├── test_report_generator.py
│   └── fixtures/
│       └── sample_apks/               # 测试用APK文件
├── scripts/
│   ├── setup_database.py             # 数据库初始化脚本
│   ├── update_signatures.py          # 特征库更新脚本
│   └── batch_analysis.py             # 批量分析脚本
├── docs/
│   ├── API.md                         # API文档
│   ├── USAGE.md                       # 使用说明
│   └── ARCHITECTURE.md               # 架构说明
└── logs/                              # 日志文件目录
```

## 主要目录说明

- **src/core/**: 核心分析模块，包含APK解析、特征提取等功能
- **src/database/**: 数据库相关模块，负责特征存储和检索
- **src/analysis/**: 分析算法模块，包含风险评分和相似度匹配
- **src/reporting/**: 报告生成模块，支持JSON和HTML格式
- **src/utils/**: 通用工具模块，提供文件操作、哈希计算等功能
- **templates/**: HTML报告模板和静态资源
- **data/**: 数据存储目录，包含数据库和特征库
- **tests/**: 单元测试和集成测试
- **scripts/**: 辅助脚本，用于数据库初始化和批量处理
