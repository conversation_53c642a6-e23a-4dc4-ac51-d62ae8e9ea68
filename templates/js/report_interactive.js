/**
 * Android APP Analytics 报告交互功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有交互功能
    initSmoothScrolling();
    initNavigation();
    initTooltips();
    initSearchFunctionality();
    initExpandableContent();
    initExportFunctions();
    
    console.log('Android APP Analytics 报告已加载');
});

/**
 * 平滑滚动导航
 */
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                // 更新导航状态
                navLinks.forEach(nl => nl.classList.remove('active'));
                this.classList.add('active');
                
                // 平滑滚动到目标位置
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * 导航高亮跟随
 */
function initNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link[href^="#"]');
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (window.pageYOffset >= sectionTop - 100) {
                current = section.getAttribute('id');
            }
        });
        
        // 更新导航高亮
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    });
}

/**
 * 初始化工具提示
 */
function initTooltips() {
    // 为代码元素添加复制功能
    const codeElements = document.querySelectorAll('code');
    
    codeElements.forEach(code => {
        code.style.cursor = 'pointer';
        code.title = '点击复制';
        
        code.addEventListener('click', function() {
            copyToClipboard(this.textContent);
            showToast('已复制到剪贴板');
        });
    });
    
    // 为哈希值添加展开/收缩功能
    const hashElements = document.querySelectorAll('code[title*="hash"], code[title*="Hash"]');
    
    hashElements.forEach(hash => {
        const fullText = hash.textContent;
        if (fullText.length > 16) {
            const shortText = fullText.substring(0, 8) + '...' + fullText.substring(fullText.length - 8);
            hash.textContent = shortText;
            hash.dataset.fullText = fullText;
            hash.dataset.shortText = shortText;
            hash.title = '点击展开完整哈希值';
            
            hash.addEventListener('click', function(e) {
                e.stopPropagation();
                if (this.textContent === this.dataset.shortText) {
                    this.textContent = this.dataset.fullText;
                    this.title = '点击收缩哈希值';
                } else {
                    this.textContent = this.dataset.shortText;
                    this.title = '点击展开完整哈希值';
                }
            });
        }
    });
}

/**
 * 搜索功能
 */
function initSearchFunctionality() {
    // 创建搜索框
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container mb-3';
    searchContainer.innerHTML = `
        <div class="input-group">
            <input type="text" class="form-control" id="reportSearch" placeholder="搜索报告内容...">
            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="searchResults" class="search-results mt-2" style="display: none;"></div>
    `;
    
    // 插入到主内容区域顶部
    const mainContent = document.querySelector('.col-md-9');
    if (mainContent) {
        mainContent.insertBefore(searchContainer, mainContent.firstChild);
    }
    
    const searchInput = document.getElementById('reportSearch');
    const clearButton = document.getElementById('clearSearch');
    const searchResults = document.getElementById('searchResults');
    
    // 搜索功能
    searchInput.addEventListener('input', function() {
        const query = this.value.trim().toLowerCase();
        
        if (query.length < 2) {
            searchResults.style.display = 'none';
            clearHighlights();
            return;
        }
        
        performSearch(query);
    });
    
    // 清除搜索
    clearButton.addEventListener('click', function() {
        searchInput.value = '';
        searchResults.style.display = 'none';
        clearHighlights();
    });
}

/**
 * 执行搜索
 */
function performSearch(query) {
    const searchableElements = document.querySelectorAll('p, td, li, .alert, .component-name, .domain-name');
    const results = [];
    
    clearHighlights();
    
    searchableElements.forEach((element, index) => {
        const text = element.textContent.toLowerCase();
        if (text.includes(query)) {
            // 高亮匹配文本
            highlightText(element, query);
            
            // 添加到搜索结果
            const section = element.closest('section');
            if (section) {
                results.push({
                    element: element,
                    section: section.id,
                    sectionTitle: section.querySelector('h4, h5')?.textContent || '未知部分',
                    preview: getTextPreview(element.textContent, query)
                });
            }
        }
    });
    
    displaySearchResults(results);
}

/**
 * 高亮文本
 */
function highlightText(element, query) {
    const regex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
    const originalHTML = element.innerHTML;
    
    // 避免重复高亮
    if (originalHTML.includes('<mark class="search-highlight">')) {
        return;
    }
    
    element.innerHTML = originalHTML.replace(regex, '<mark class="search-highlight">$1</mark>');
}

/**
 * 清除高亮
 */
function clearHighlights() {
    const highlights = document.querySelectorAll('.search-highlight');
    highlights.forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

/**
 * 显示搜索结果
 */
function displaySearchResults(results) {
    const searchResults = document.getElementById('searchResults');
    
    if (results.length === 0) {
        searchResults.innerHTML = '<div class="alert alert-info">未找到匹配的内容</div>';
        searchResults.style.display = 'block';
        return;
    }
    
    const resultsHTML = results.map(result => `
        <div class="search-result-item" data-section="${result.section}">
            <strong>${result.sectionTitle}</strong>
            <p class="text-muted mb-1">${result.preview}</p>
        </div>
    `).join('');
    
    searchResults.innerHTML = `
        <div class="alert alert-success">找到 ${results.length} 个匹配结果</div>
        ${resultsHTML}
    `;
    searchResults.style.display = 'block';
    
    // 添加点击跳转功能
    searchResults.querySelectorAll('.search-result-item').forEach(item => {
        item.style.cursor = 'pointer';
        item.addEventListener('click', function() {
            const sectionId = this.dataset.section;
            const section = document.getElementById(sectionId);
            if (section) {
                section.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

/**
 * 可展开内容
 */
function initExpandableContent() {
    // 为长列表添加展开/收缩功能
    const longLists = document.querySelectorAll('.table tbody');
    
    longLists.forEach(tbody => {
        const rows = tbody.querySelectorAll('tr');
        if (rows.length > 10) {
            // 隐藏超过10行的内容
            for (let i = 10; i < rows.length; i++) {
                rows[i].style.display = 'none';
                rows[i].classList.add('expandable-row');
            }
            
            // 添加展开按钮
            const expandButton = document.createElement('tr');
            expandButton.innerHTML = `
                <td colspan="100%" class="text-center">
                    <button class="btn btn-outline-primary btn-sm expand-btn">
                        显示更多 (${rows.length - 10} 项)
                    </button>
                </td>
            `;
            tbody.appendChild(expandButton);
            
            expandButton.querySelector('.expand-btn').addEventListener('click', function() {
                const hiddenRows = tbody.querySelectorAll('.expandable-row');
                const isExpanded = this.textContent.includes('收起');
                
                if (isExpanded) {
                    hiddenRows.forEach(row => row.style.display = 'none');
                    this.textContent = `显示更多 (${hiddenRows.length} 项)`;
                } else {
                    hiddenRows.forEach(row => row.style.display = '');
                    this.textContent = '收起';
                }
            });
        }
    });
}

/**
 * 导出功能
 */
function initExportFunctions() {
    // 添加导出按钮到导航栏
    const navbar = document.querySelector('.navbar .container');
    if (navbar) {
        const exportButtons = document.createElement('div');
        exportButtons.className = 'navbar-nav';
        exportButtons.innerHTML = `
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-download"></i> 导出
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> 导出PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="printReport()">
                        <i class="fas fa-print"></i> 打印报告
                    </a></li>
                </ul>
            </div>
        `;
        navbar.appendChild(exportButtons);
    }
}

/**
 * 工具函数
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
    } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
    }
}

function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #28a745;
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 9999;
        animation: slideIn 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => document.body.removeChild(toast), 300);
    }, 2000);
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function getTextPreview(text, query) {
    const index = text.toLowerCase().indexOf(query.toLowerCase());
    const start = Math.max(0, index - 30);
    const end = Math.min(text.length, index + query.length + 30);
    
    let preview = text.substring(start, end);
    if (start > 0) preview = '...' + preview;
    if (end < text.length) preview = preview + '...';
    
    return preview;
}

function exportToPDF() {
    window.print();
}

function printReport() {
    window.print();
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .search-highlight {
        background-color: #fff3cd;
        padding: 2px 4px;
        border-radius: 3px;
    }
    
    .search-result-item {
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        margin-bottom: 8px;
        transition: background-color 0.3s ease;
    }
    
    .search-result-item:hover {
        background-color: #f8f9fa;
    }
`;
document.head.appendChild(style);
