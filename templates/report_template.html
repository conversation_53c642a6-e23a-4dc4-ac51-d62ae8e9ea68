<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Android APP Analytics - {{ package_name }} 分析报告</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- 自定义样式 -->
    <link href="css/report_styles.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt"></i> Android APP Analytics
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    生成时间: {{ generation_time | format_timestamp }}
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- 侧边栏导航 -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> 报告导航</h5>
                    </div>
                    <div class="card-body p-0">
                        <nav class="nav nav-pills flex-column">
                            <a class="nav-link active" href="#overview">概览</a>
                            <a class="nav-link" href="#risk-assessment">风险评估</a>
                            <a class="nav-link" href="#basic-info">基础信息</a>
                            <a class="nav-link" href="#permissions">权限分析</a>
                            <a class="nav-link" href="#components">组件分析</a>
                            <a class="nav-link" href="#certificates">证书信息</a>
                            <a class="nav-link" href="#code-analysis">代码分析</a>
                            <a class="nav-link" href="#resources">资源分析</a>
                            <a class="nav-link" href="#network">网络特征</a>
                            <a class="nav-link" href="#native-libs">原生库</a>
                            <a class="nav-link" href="#similarity">相似度匹配</a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 主内容区域 -->
            <div class="col-md-9">
                <!-- 概览部分 -->
                <section id="overview" class="mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4><i class="fas fa-mobile-alt"></i> 应用概览</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5>{{ report_data.apk_info.package_name }}</h5>
                                    <p class="text-muted">{{ report_data.apk_info.version_name }} ({{ report_data.apk_info.version_code }})</p>
                                    
                                    <div class="row mt-3">
                                        <div class="col-sm-6">
                                            <strong>文件大小:</strong> {{ report_data.apk_info.file_size | format_file_size }}
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>目标SDK:</strong> {{ report_data.apk_info.target_sdk_version }}
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>最小SDK:</strong> {{ report_data.apk_info.min_sdk_version }}
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>分析时间:</strong> {{ report_data.analysis_metadata.analysis_date | format_timestamp }}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 text-center">
                                    <!-- 风险评分仪表盘 -->
                                    <div class="risk-score-container">
                                        <canvas id="riskScoreChart" width="200" height="200"></canvas>
                                        <div class="risk-score-text">
                                            <h3 class="text-{{ report_data.risk_assessment.overall_risk_score | risk_level_color }}">
                                                {{ "%.1f" | format(report_data.risk_assessment.overall_risk_score) }}
                                            </h3>
                                            <p class="text-{{ report_data.risk_assessment.overall_risk_score | risk_level_color }}">
                                                {{ report_data.risk_assessment.overall_risk_score | risk_level_text }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 风险评估部分 -->
                <section id="risk-assessment" class="mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h4><i class="fas fa-exclamation-triangle"></i> 风险评估</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <!-- 风险分项评分 -->
                                    <h5>分项风险评分</h5>
                                    <div class="risk-breakdown">
                                        {% for risk_type, score in [
                                            ('证书风险', report_data.risk_assessment.certificate_risk_score),
                                            ('权限风险', report_data.risk_assessment.permission_risk_score),
                                            ('组件风险', report_data.risk_assessment.component_risk_score),
                                            ('代码风险', report_data.risk_assessment.code_risk_score),
                                            ('网络风险', report_data.risk_assessment.network_risk_score)
                                        ] %}
                                        <div class="mb-2">
                                            <div class="d-flex justify-content-between">
                                                <span>{{ risk_type }}</span>
                                                <span class="badge bg-{{ score | risk_level_color }}">{{ "%.1f" | format(score) }}</span>
                                            </div>
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-{{ score | risk_level_color }}" 
                                                     style="width: {{ (score / 10 * 100) | round }}%"></div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <!-- 风险因子 -->
                                    <h5>主要风险因子</h5>
                                    <div class="risk-factors">
                                        {% if report_data.risk_assessment.risk_factors %}
                                            {% for factor in report_data.risk_assessment.risk_factors %}
                                            <div class="alert alert-{{ factor.severity | default('info') }} py-2">
                                                <strong>{{ factor.category }}:</strong> {{ factor.description }}
                                                <span class="badge bg-{{ factor.severity | default('secondary') }} float-end">
                                                    {{ factor.score | default(0) }}
                                                </span>
                                            </div>
                                            {% endfor %}
                                        {% else %}
                                            <p class="text-muted">未发现明显风险因子</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 基础信息部分 -->
                <section id="basic-info" class="mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-info-circle"></i> 基础信息</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>包名</strong></td>
                                            <td>{{ report_data.apk_info.package_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>版本名称</strong></td>
                                            <td>{{ report_data.apk_info.version_name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>版本代码</strong></td>
                                            <td>{{ report_data.apk_info.version_code }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>目标SDK版本</strong></td>
                                            <td>{{ report_data.apk_info.target_sdk_version }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>最小SDK版本</strong></td>
                                            <td>{{ report_data.apk_info.min_sdk_version }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>文件大小</strong></td>
                                            <td>{{ report_data.apk_info.file_size | format_file_size }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>MD5</strong></td>
                                            <td><code>{{ report_data.apk_info.file_md5 | truncate_text(32) }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>SHA1</strong></td>
                                            <td><code>{{ report_data.apk_info.file_sha1 | truncate_text(32) }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>SHA256</strong></td>
                                            <td><code>{{ report_data.apk_info.file_sha256 | truncate_text(32) }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>分析时间</strong></td>
                                            <td>{{ report_data.analysis_metadata.analysis_date | format_timestamp }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 权限分析部分 -->
                <section id="permissions" class="mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-key"></i> 权限分析</h4>
                        </div>
                        <div class="card-body">
                            {% if report_data.features.manifest_features.permissions %}
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>权限统计</h5>
                                    <canvas id="permissionChart" width="300" height="200"></canvas>
                                </div>
                                <div class="col-md-6">
                                    <h5>危险权限列表</h5>
                                    <div class="permission-list" style="max-height: 300px; overflow-y: auto;">
                                        {% for permission in report_data.features.manifest_features.permissions %}
                                        {% if permission.level == 'dangerous' %}
                                        <div class="alert alert-warning py-2 mb-2">
                                            <strong>{{ permission.name }}</strong>
                                            <span class="badge bg-warning text-dark float-end">{{ permission.level }}</span>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <h5>完整权限列表</h5>
                                <div class="table-responsive">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                            <tr>
                                                <th>权限名称</th>
                                                <th>保护级别</th>
                                                <th>类型</th>
                                                <th>风险等级</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for permission in report_data.features.manifest_features.permissions %}
                                            <tr>
                                                <td><code>{{ permission.name }}</code></td>
                                                <td>{{ permission.protection_level | default('unknown') }}</td>
                                                <td>
                                                    {% if permission.is_custom %}
                                                    <span class="badge bg-info">自定义</span>
                                                    {% else %}
                                                    <span class="badge bg-secondary">系统</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ permission.level | risk_level_color }}">
                                                        {{ permission.level | default('normal') }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% else %}
                            <p class="text-muted">未发现权限信息</p>
                            {% endif %}
                        </div>
                    </div>
                </section>

                <!-- 更多部分将在后续添加... -->
                
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义JavaScript -->
    <script src="js/report_interactive.js"></script>
    
    <script>
        // 初始化风险评分图表
        const riskScore = {{ report_data.risk_assessment.overall_risk_score }};
        const ctx = document.getElementById('riskScoreChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [riskScore, 10 - riskScore],
                    backgroundColor: [
                        riskScore >= 8 ? '#dc3545' : riskScore >= 6 ? '#fd7e14' : riskScore >= 4 ? '#0dcaf0' : '#198754',
                        '#e9ecef'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // 权限统计图表
        {% if report_data.features.manifest_features.permissions %}
        const permissionLevels = {};
        {% for permission in report_data.features.manifest_features.permissions %}
        const level = '{{ permission.level | default("normal") }}';
        permissionLevels[level] = (permissionLevels[level] || 0) + 1;
        {% endfor %}
        
        const permCtx = document.getElementById('permissionChart').getContext('2d');
        new Chart(permCtx, {
            type: 'pie',
            data: {
                labels: Object.keys(permissionLevels),
                datasets: [{
                    data: Object.values(permissionLevels),
                    backgroundColor: ['#198754', '#0dcaf0', '#fd7e14', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        {% endif %}
    </script>
</body>
</html>
