/* Android APP Analytics 报告样式 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 8px;
    color: #28a745;
}

/* 侧边栏导航 */
.nav-pills .nav-link {
    color: #495057;
    border-radius: 0;
    padding: 12px 16px;
    margin-bottom: 2px;
    transition: all 0.3s ease;
}

.nav-pills .nav-link:hover {
    background-color: #e9ecef;
    color: #007bff;
}

.nav-pills .nav-link.active {
    background-color: #007bff;
    color: white;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    border-radius: 8px;
}

.card-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #fff;
    border-radius: 8px 8px 0 0 !important;
    padding: 1rem 1.25rem;
}

.card-header h4, .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.card-header i {
    margin-right: 8px;
}

/* 风险评分容器 */
.risk-score-container {
    position: relative;
    display: inline-block;
    width: 200px;
    height: 200px;
}

.risk-score-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.risk-score-text h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
}

.risk-score-text p {
    font-size: 1rem;
    margin: 0;
    font-weight: 500;
}

/* 风险分项评分 */
.risk-breakdown .progress {
    background-color: #e9ecef;
}

.risk-breakdown .badge {
    min-width: 40px;
    font-size: 0.875rem;
}

/* 风险因子 */
.risk-factors .alert {
    border-left: 4px solid;
    border-left-color: inherit;
}

.risk-factors .alert-danger {
    border-left-color: #dc3545;
}

.risk-factors .alert-warning {
    border-left-color: #fd7e14;
}

.risk-factors .alert-info {
    border-left-color: #0dcaf0;
}

.risk-factors .alert-success {
    border-left-color: #198754;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.table code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.875rem;
    color: #e83e8c;
}

/* 权限列表 */
.permission-list .alert {
    border-left: 4px solid;
}

.permission-list .alert-warning {
    border-left-color: #fd7e14;
    background-color: #fff3cd;
}

.permission-list .alert-danger {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

/* 组件分析 */
.component-item {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    background-color: #fff;
    transition: box-shadow 0.3s ease;
}

.component-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.component-name {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #495057;
}

.component-exported {
    color: #dc3545;
    font-weight: bold;
}

.component-not-exported {
    color: #28a745;
}

/* 代码分析 */
.code-metrics {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
}

.code-metrics .metric-item {
    text-align: center;
    padding: 10px;
}

.code-metrics .metric-value {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.code-metrics .metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 网络特征 */
.network-domain {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 8px;
}

.network-domain .domain-name {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #007bff;
}

.network-domain .confidence-score {
    float: right;
}

/* 相似度匹配 */
.similarity-match {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    background-color: #fff;
}

.similarity-match.high-risk {
    border-left: 4px solid #dc3545;
    background-color: #fff5f5;
}

.similarity-match.medium-risk {
    border-left: 4px solid #fd7e14;
    background-color: #fffbf0;
}

.similarity-match.low-risk {
    border-left: 4px solid #28a745;
    background-color: #f8fff8;
}

.similarity-score {
    font-size: 1.2rem;
    font-weight: bold;
}

/* 证书信息 */
.certificate-info {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 15px;
}

.certificate-fingerprint {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    background-color: #fff;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

/* 原生库 */
.native-library {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
}

.native-library .lib-name {
    font-family: 'Courier New', monospace;
    font-weight: bold;
}

.native-library .lib-arch {
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .risk-score-container {
        width: 150px;
        height: 150px;
    }
    
    .risk-score-text h3 {
        font-size: 2rem;
    }
    
    .code-metrics .metric-value {
        font-size: 1.5rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 打印样式 */
@media print {
    .navbar,
    .nav-pills {
        display: none !important;
    }
    
    .col-md-3 {
        display: none !important;
    }
    
    .col-md-9 {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    .card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .risk-score-container {
        display: none;
    }
}
