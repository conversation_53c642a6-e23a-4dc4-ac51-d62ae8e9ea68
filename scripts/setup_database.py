#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库表结构和初始化基础数据
"""

import os
import sys
import sqlite3
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import Settings
from config.logging_config import setup_logging


def create_database_tables(db_path: str):
    """创建数据库表结构"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取schema文件
        schema_path = project_root / "src" / "database" / "schema.sql"
        
        if not schema_path.exists():
            raise FileNotFoundError(f"Schema文件不存在: {schema_path}")
        
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        # 创建数据库连接
        conn = sqlite3.connect(db_path)
        
        try:
            # 执行建表语句
            conn.executescript(schema_sql)
            conn.commit()
            logger.info("数据库表结构创建成功")
            
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"创建数据库表结构失败: {str(e)}")
        raise


def initialize_malware_signatures(db_path: str):
    """初始化恶意软件特征库"""
    logger = logging.getLogger(__name__)
    
    try:
        # 示例恶意软件特征数据
        sample_signatures = [
            {
                'signature_type': 'cert',
                'signature_value': 'a40da80a59d170caa950cf15c18c454d47a39b26989d8b640ecd745ba71bf5dc',
                'malware_family': 'FakeInstaller',
                'severity_level': 5,
                'description': '已知恶意证书哈希',
                'source': 'VirusTotal'
            },
            {
                'signature_type': 'domain',
                'signature_value': 'malicious-domain.com',
                'malware_family': 'Adware',
                'severity_level': 3,
                'description': '已知恶意域名',
                'source': 'Manual Analysis'
            },
            {
                'signature_type': 'permission',
                'signature_value': 'android.permission.SEND_SMS|android.permission.READ_SMS|android.permission.RECEIVE_SMS',
                'malware_family': 'SMS Trojan',
                'severity_level': 4,
                'description': '短信木马常用权限组合',
                'source': 'Pattern Analysis'
            },
            {
                'signature_type': 'code_hash',
                'signature_value': 'b5d4045c3f466fa91fe2cc6abe79232a1a57cdf104f7a26e716e0a1e2789df78',
                'malware_family': 'Banking Trojan',
                'severity_level': 5,
                'description': '银行木马代码特征哈希',
                'source': 'Security Research'
            }
        ]
        
        conn = sqlite3.connect(db_path)
        
        try:
            cursor = conn.cursor()
            
            # 插入示例特征数据
            for sig in sample_signatures:
                cursor.execute("""
                    INSERT INTO malware_signatures (
                        signature_type, signature_value, malware_family, 
                        severity_level, description, source
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    sig['signature_type'],
                    sig['signature_value'],
                    sig['malware_family'],
                    sig['severity_level'],
                    sig['description'],
                    sig['source']
                ))
            
            conn.commit()
            logger.info(f"已初始化 {len(sample_signatures)} 个恶意软件特征")
            
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"初始化恶意软件特征库失败: {str(e)}")
        raise


def create_sample_config():
    """创建示例配置文件"""
    logger = logging.getLogger(__name__)
    
    try:
        config_path = project_root / "config" / "sample_config.json"
        
        sample_config = {
            "version": "1.0.0",
            "debug": False,
            "analysis": {
                "max_workers": 4,
                "task_timeout": 600,
                "enable_jadx": True,
                "enable_deep_analysis": True,
                "similarity_threshold": 0.8,
                "risk_threshold": 7.0
            },
            "tools": {
                "jadx": {
                    "path": "jadx",
                    "timeout": 300
                },
                "apktool": {
                    "path": "apktool",
                    "timeout": 180
                }
            },
            "security": {
                "max_file_size": 104857600,
                "allowed_extensions": [".apk", ".APK"],
                "scan_timeout": 1800
            },
            "logging": {
                "level": "INFO",
                "file_max_size": 10485760,
                "backup_count": 5
            },
            "reporting": {
                "include_screenshots": True,
                "include_source_code": False,
                "max_code_lines": 100,
                "export_formats": ["html", "json"]
            }
        }
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(sample_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"示例配置文件已创建: {config_path}")
        
    except Exception as e:
        logger.error(f"创建示例配置文件失败: {str(e)}")


def verify_database(db_path: str):
    """验证数据库完整性"""
    logger = logging.getLogger(__name__)
    
    try:
        conn = sqlite3.connect(db_path)
        
        try:
            cursor = conn.cursor()
            
            # 检查所有表是否存在
            expected_tables = [
                'apk_info', 'certificates', 'permissions', 'components',
                'code_features', 'resource_features', 'native_libraries',
                'network_features', 'malware_signatures', 'risk_assessments',
                'analysis_tasks'
            ]
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            missing_tables = set(expected_tables) - set(existing_tables)
            if missing_tables:
                logger.error(f"缺少数据库表: {missing_tables}")
                return False
            
            # 检查恶意软件特征数据
            cursor.execute("SELECT COUNT(*) FROM malware_signatures")
            sig_count = cursor.fetchone()[0]
            
            logger.info(f"数据库验证通过:")
            logger.info(f"  - 表数量: {len(existing_tables)}")
            logger.info(f"  - 恶意软件特征: {sig_count} 条")
            
            return True
            
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"数据库验证失败: {str(e)}")
        return False


def main():
    """主函数"""
    # 设置日志
    logger = setup_logging("INFO")
    logger.info("开始初始化Android APP Analytics数据库")
    
    try:
        # 加载配置
        settings = Settings()
        
        # 确保数据库目录存在
        db_dir = Path(settings.database_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"数据库路径: {settings.database_path}")
        
        # 检查数据库是否已存在
        if os.path.exists(settings.database_path):
            response = input("数据库文件已存在，是否重新创建？(y/N): ")
            if response.lower() != 'y':
                logger.info("跳过数据库初始化")
                return
            
            # 备份现有数据库
            backup_path = f"{settings.database_path}.backup"
            import shutil
            shutil.copy2(settings.database_path, backup_path)
            logger.info(f"已备份现有数据库到: {backup_path}")
        
        # 创建数据库表结构
        logger.info("创建数据库表结构...")
        create_database_tables(settings.database_path)
        
        # 初始化恶意软件特征库
        logger.info("初始化恶意软件特征库...")
        initialize_malware_signatures(settings.database_path)
        
        # 创建示例配置文件
        logger.info("创建示例配置文件...")
        create_sample_config()
        
        # 验证数据库
        logger.info("验证数据库完整性...")
        if verify_database(settings.database_path):
            logger.info("✅ 数据库初始化完成！")
            
            print("\n" + "="*50)
            print("🎉 Android APP Analytics 数据库初始化成功！")
            print("="*50)
            print(f"📁 数据库位置: {settings.database_path}")
            print(f"📄 示例配置: {project_root}/config/sample_config.json")
            print("\n📖 使用说明:")
            print("   python src/main.py -f your_app.apk -o ./reports")
            print("\n🔗 更多信息请查看 README.md")
            print("="*50)
        else:
            logger.error("❌ 数据库验证失败")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
