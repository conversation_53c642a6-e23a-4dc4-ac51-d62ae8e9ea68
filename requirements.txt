# Android APP Analytics - Python依赖包列表

# 核心分析库
androguard>=3.4.0                    # APK分析和反编译
python-magic>=0.4.27                 # 文件类型检测

# 数据库
sqlite3                               # SQLite数据库（Python内置）

# 模板引擎
Jinja2>=3.1.0                        # HTML报告模板引擎
MarkupSafe>=2.1.0                    # Jinja2依赖

# 网络和HTTP
requests>=2.28.0                     # HTTP请求库
urllib3>=1.26.0                      # HTTP客户端
beautifulsoup4>=4.11.0               # HTML解析

# 图像处理
Pillow>=9.0.0                        # 图像处理（用于图标分析）
imagehash>=4.3.0                     # 感知哈希算法

# 加密和哈希
cryptography>=3.4.8                  # 加密算法和证书处理
hashlib                              # 哈希算法（Python内置）

# 文件处理
zipfile                               # ZIP文件处理（Python内置）
pathlib                              # 路径处理（Python内置）

# 数据处理
json                                  # JSON处理（Python内置）
csv                                   # CSV处理（Python内置）
xml.etree.ElementTree                # XML处理（Python内置）

# 并发处理
concurrent.futures                   # 并发执行（Python内置）
threading                            # 线程处理（Python内置）

# 任务队列（可选）
celery>=5.2.0                        # 分布式任务队列
redis>=4.3.0                         # Redis缓存（Celery后端）

# 日志和配置
logging                               # 日志处理（Python内置）
configparser                         # 配置文件解析（Python内置）

# 时间处理
datetime                              # 时间处理（Python内置）

# 正则表达式
re                                    # 正则表达式（Python内置）

# 系统和进程
subprocess                            # 子进程调用（Python内置）
os                                    # 操作系统接口（Python内置）
sys                                   # 系统参数（Python内置）
shutil                                # 高级文件操作（Python内置）

# 数据验证
typing                                # 类型提示（Python内置）

# 开发和测试工具
pytest>=7.0.0                        # 单元测试框架
pytest-cov>=4.0.0                    # 测试覆盖率
black>=22.0.0                        # 代码格式化
flake8>=5.0.0                        # 代码检查
mypy>=0.991                          # 类型检查

# 文档生成
sphinx>=5.0.0                        # 文档生成
sphinx-rtd-theme>=1.0.0              # 文档主题

# 性能分析
memory-profiler>=0.60.0              # 内存分析
psutil>=5.9.0                        # 系统和进程监控

# 可选：机器学习（用于高级相似度分析）
scikit-learn>=1.1.0                  # 机器学习库
numpy>=1.21.0                        # 数值计算
pandas>=1.4.0                        # 数据分析

# 可选：图形界面（如果需要GUI版本）
tkinter                               # GUI框架（Python内置）
PyQt5>=5.15.0                        # 高级GUI框架（可选）

# 可选：Web界面（如果需要Web版本）
Flask>=2.2.0                         # 轻量级Web框架
Flask-SQLAlchemy>=2.5.0              # Flask数据库扩展
gunicorn>=20.1.0                     # WSGI服务器

# 安全扫描增强
yara-python>=4.2.0                   # YARA规则引擎
ssdeep>=3.4                          # 模糊哈希

# 反混淆和代码分析增强
capstone>=4.0.2                      # 反汇编引擎
keystone-engine>=0.9.2               # 汇编引擎

# 网络分析增强
tldextract>=3.4.0                    # 域名解析
ipaddress                             # IP地址处理（Python内置）

# 报告增强
matplotlib>=3.5.0                    # 图表生成
seaborn>=0.11.0                      # 统计图表
plotly>=5.10.0                       # 交互式图表

# 配置管理
python-dotenv>=0.20.0                # 环境变量管理
PyYAML>=6.0                          # YAML配置文件支持

# 命令行界面增强
click>=8.1.0                         # 命令行接口框架
colorama>=0.4.5                      # 彩色终端输出
tqdm>=4.64.0                         # 进度条

# 缓存
diskcache>=5.4.0                     # 磁盘缓存

# 版本信息
# Python >= 3.8 required
# 建议使用 Python 3.9 或更高版本以获得最佳性能

# 安装说明：
# pip install -r requirements.txt
#
# 对于开发环境，还可以安装：
# pip install -r requirements.txt -e .
#
# 注意：某些包可能需要系统级依赖，请参考各包的安装文档
