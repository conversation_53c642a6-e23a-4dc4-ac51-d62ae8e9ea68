#!/usr/bin/env python3
"""
Android APP Analytics - 基础使用示例
演示如何使用API进行APK分析
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.main import AndroidAppAnalytics


def analyze_single_apk_example():
    """单个APK分析示例"""
    print("=" * 60)
    print("📱 Android APP Analytics - 单个APK分析示例")
    print("=" * 60)
    
    # APK文件路径（请替换为实际的APK文件路径）
    apk_path = "path/to/your/app.apk"
    output_dir = "./analysis_results"
    
    # 检查APK文件是否存在
    if not os.path.exists(apk_path):
        print(f"❌ APK文件不存在: {apk_path}")
        print("请将APK文件路径替换为实际存在的文件")
        return
    
    try:
        # 初始化分析器
        print("🔧 初始化Android APP Analytics...")
        analyzer = AndroidAppAnalytics()
        
        # 执行分析
        print(f"🔍 开始分析APK: {apk_path}")
        result = analyzer.analyze_single_apk(apk_path, output_dir)
        
        if result['success']:
            print("✅ 分析完成！")
            print(f"📦 应用包名: {result['package_name']}")
            print(f"⚠️  风险评分: {result['risk_score']:.1f}/10.0")
            print(f"📊 HTML报告: {result['html_report']}")
            print(f"📄 JSON报告: {result['json_report']}")
            
            # 显示分析摘要
            summary = result['analysis_summary']
            print("\n📋 分析摘要:")
            print(f"   权限数量: {summary['permissions_count']}")
            print(f"   组件数量: {summary['components_count']}")
            print(f"   网络特征: {summary['network_features_count']}")
            print(f"   原生库数量: {summary['native_libraries_count']}")
            print(f"   高风险匹配: {summary['high_risk_matches']}")
            
        else:
            print(f"❌ 分析失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")


def batch_analysis_example():
    """批量分析示例"""
    print("\n" + "=" * 60)
    print("📱 Android APP Analytics - 批量分析示例")
    print("=" * 60)
    
    # APK文件目录（请替换为实际的目录路径）
    apk_directory = "path/to/apk/directory"
    output_dir = "./batch_analysis_results"
    
    # 检查目录是否存在
    if not os.path.exists(apk_directory):
        print(f"❌ APK目录不存在: {apk_directory}")
        print("请将目录路径替换为实际存在的目录")
        return
    
    try:
        # 初始化分析器
        print("🔧 初始化Android APP Analytics...")
        analyzer = AndroidAppAnalytics()
        
        # 查找APK文件
        apk_files = []
        for ext in ['*.apk', '*.APK']:
            apk_files.extend(Path(apk_directory).glob(ext))
        
        if not apk_files:
            print(f"❌ 在目录 {apk_directory} 中未找到APK文件")
            return
        
        apk_paths = [str(f) for f in apk_files]
        print(f"🔍 找到 {len(apk_paths)} 个APK文件")
        
        # 执行批量分析
        print("🚀 开始批量分析...")
        results = analyzer.analyze_batch_apks(apk_paths, output_dir)
        
        # 统计结果
        successful = [r for r in results if r.get('success')]
        failed = [r for r in results if not r.get('success')]
        
        print("✅ 批量分析完成！")
        print(f"📊 成功分析: {len(successful)}/{len(results)} 个文件")
        
        if successful:
            avg_risk = sum(r['risk_score'] for r in successful) / len(successful)
            high_risk = [r for r in successful if r['risk_score'] >= 7.0]
            
            print(f"⚠️  平均风险评分: {avg_risk:.1f}/10.0")
            print(f"🚨 高风险应用: {len(high_risk)} 个")
            
            if high_risk:
                print("\n🚨 高风险应用列表:")
                for app in high_risk:
                    print(f"   - {app['package_name']}: {app['risk_score']:.1f}")
        
        if failed:
            print(f"\n❌ 分析失败的文件:")
            for fail in failed:
                print(f"   - {fail['apk_path']}: {fail['error']}")
                
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")


def api_usage_example():
    """API使用示例"""
    print("\n" + "=" * 60)
    print("📱 Android APP Analytics - API使用示例")
    print("=" * 60)
    
    try:
        # 导入核心模块
        from src.core.apk_parser import APKParser
        from src.core.feature_extractor import FeatureExtractor
        from src.database.database_manager import DatabaseManager
        from config.settings import Settings
        
        # 初始化组件
        settings = Settings()
        apk_parser = APKParser()
        feature_extractor = FeatureExtractor(settings)
        db_manager = DatabaseManager(settings.database_path)
        
        print("🔧 核心组件初始化完成")
        
        # 示例：解析APK基础信息
        apk_path = "path/to/your/app.apk"
        if os.path.exists(apk_path):
            print(f"🔍 解析APK基础信息: {apk_path}")
            apk_info = apk_parser.parse_apk(apk_path)
            
            print(f"📦 包名: {apk_info.get('package_name', 'unknown')}")
            print(f"📱 版本: {apk_info.get('version_name', 'unknown')}")
            print(f"📏 文件大小: {apk_info.get('file_size', 0)} bytes")
            print(f"🔒 已签名: {apk_info.get('is_signed', False)}")
        else:
            print("⚠️  请提供有效的APK文件路径以测试API功能")
        
        # 示例：查询恶意软件特征
        print("\n🛡️  查询恶意软件特征库...")
        signatures = db_manager.get_malware_signatures()
        print(f"📊 特征库包含 {len(signatures)} 个恶意软件特征")
        
        if signatures:
            print("🔍 特征类型分布:")
            sig_types = {}
            for sig in signatures:
                sig_type = sig['signature_type']
                sig_types[sig_type] = sig_types.get(sig_type, 0) + 1
            
            for sig_type, count in sig_types.items():
                print(f"   - {sig_type}: {count} 个")
                
    except Exception as e:
        print(f"❌ API示例执行失败: {str(e)}")


def main():
    """主函数"""
    print("🚀 Android APP Analytics - 使用示例")
    print("本示例演示了如何使用Android APP Analytics进行APK安全分析")
    
    # 检查项目结构
    required_files = [
        project_root / "src" / "main.py",
        project_root / "src" / "database" / "schema.sql",
        project_root / "config" / "settings.py"
    ]
    
    missing_files = [f for f in required_files if not f.exists()]
    if missing_files:
        print("❌ 项目文件不完整，请确保以下文件存在:")
        for f in missing_files:
            print(f"   - {f}")
        return
    
    print("✅ 项目结构检查通过")
    
    # 运行示例
    try:
        # 1. 单个APK分析示例
        analyze_single_apk_example()
        
        # 2. 批量分析示例
        batch_analysis_example()
        
        # 3. API使用示例
        api_usage_example()
        
        print("\n" + "=" * 60)
        print("🎉 示例运行完成！")
        print("💡 提示: 请将示例中的文件路径替换为实际的APK文件路径")
        print("📖 更多信息请查看 README.md 和项目文档")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {str(e)}")


if __name__ == '__main__':
    main()
