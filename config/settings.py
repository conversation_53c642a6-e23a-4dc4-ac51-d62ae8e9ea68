"""
全局配置文件
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class Settings:
    """全局配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置
        
        Args:
            config_path: 自定义配置文件路径
        """
        # 项目根目录
        self.project_root = Path(__file__).parent.parent
        
        # 默认配置
        self._default_config = {
            # 基础配置
            "version": "1.0.0",
            "debug": False,
            
            # 路径配置
            "data_dir": str(self.project_root / "data"),
            "temp_dir": str(self.project_root / "data" / "temp"),
            "database_path": str(self.project_root / "data" / "database" / "features.db"),
            "template_dir": str(self.project_root / "templates"),
            "log_dir": str(self.project_root / "logs"),
            
            # 外部工具配置
            "tools": {
                "jadx": {
                    "path": "jadx",  # 假设jadx在PATH中
                    "timeout": 300,  # 5分钟超时
                    "args": ["-d", "{output_dir}", "{apk_path}"]
                },
                "apktool": {
                    "path": "apktool",
                    "timeout": 180,  # 3分钟超时
                    "args": ["d", "{apk_path}", "-o", "{output_dir}"]
                },
                "aapt": {
                    "path": "aapt",
                    "timeout": 60
                }
            },
            
            # 分析配置
            "analysis": {
                "max_workers": 4,  # 并行工作线程数
                "task_timeout": 600,  # 单个任务超时时间（秒）
                "enable_jadx": True,  # 是否启用Jadx反编译
                "enable_deep_analysis": True,  # 是否启用深度分析
                "similarity_threshold": 0.8,  # 相似度阈值
                "risk_threshold": 7.0,  # 高风险阈值
            },
            
            # 数据库配置
            "database": {
                "connection_timeout": 30,
                "max_connections": 10,
                "backup_enabled": True,
                "backup_interval": 86400  # 24小时
            },
            
            # 日志配置
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_max_size": 10485760,  # 10MB
                "backup_count": 5
            },
            
            # 报告配置
            "reporting": {
                "include_screenshots": True,
                "include_source_code": False,  # 是否在报告中包含源代码
                "max_code_lines": 100,  # 报告中显示的最大代码行数
                "compress_reports": False,  # 是否压缩报告
                "export_formats": ["html", "json"]  # 支持的导出格式
            },
            
            # 网络配置
            "network": {
                "timeout": 30,
                "retries": 3,
                "user_agent": "Android-APP-Analytics/1.0",
                "proxy": None
            },
            
            # 安全配置
            "security": {
                "max_file_size": 104857600,  # 100MB
                "allowed_extensions": [".apk", ".APK"],
                "scan_timeout": 1800,  # 30分钟
                "temp_cleanup": True
            }
        }
        
        # 加载配置
        self._config = self._load_config(config_path)
        
        # 创建必要的目录
        self._create_directories()
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置文件"""
        config = self._default_config.copy()
        
        # 从环境变量加载配置
        config.update(self._load_from_env())
        
        # 从配置文件加载
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                config = self._merge_config(config, file_config)
            except Exception as e:
                logging.warning(f"加载配置文件失败: {e}")
        
        return config
    
    def _load_from_env(self) -> Dict[str, Any]:
        """从环境变量加载配置"""
        env_config = {}
        
        # 数据库路径
        if os.getenv('AAA_DATABASE_PATH'):
            env_config['database_path'] = os.getenv('AAA_DATABASE_PATH')
        
        # 临时目录
        if os.getenv('AAA_TEMP_DIR'):
            env_config['temp_dir'] = os.getenv('AAA_TEMP_DIR')
        
        # 日志级别
        if os.getenv('AAA_LOG_LEVEL'):
            env_config.setdefault('logging', {})['level'] = os.getenv('AAA_LOG_LEVEL')
        
        # 调试模式
        if os.getenv('AAA_DEBUG'):
            env_config['debug'] = os.getenv('AAA_DEBUG').lower() in ('true', '1', 'yes')
        
        # 工具路径
        if os.getenv('JADX_PATH'):
            env_config.setdefault('tools', {}).setdefault('jadx', {})['path'] = os.getenv('JADX_PATH')
        
        if os.getenv('APKTOOL_PATH'):
            env_config.setdefault('tools', {}).setdefault('apktool', {})['path'] = os.getenv('APKTOOL_PATH')
        
        return env_config
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """递归合并配置字典"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.data_dir,
            self.temp_dir,
            self.log_dir,
            os.path.dirname(self.database_path)
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self, config_path: str):
        """保存配置到文件"""
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    # 属性访问器
    @property
    def version(self) -> str:
        return self.get('version', '1.0.0')
    
    @property
    def debug(self) -> bool:
        return self.get('debug', False)
    
    @property
    def data_dir(self) -> str:
        return self.get('data_dir')
    
    @property
    def temp_dir(self) -> str:
        return self.get('temp_dir')
    
    @property
    def database_path(self) -> str:
        return self.get('database_path')
    
    @property
    def template_dir(self) -> str:
        return self.get('template_dir')
    
    @property
    def log_dir(self) -> str:
        return self.get('log_dir')
    
    @property
    def log_level(self) -> str:
        return self.get('logging.level', 'INFO')
    
    @property
    def log_file(self) -> str:
        return os.path.join(self.log_dir, 'android_app_analytics.log')
    
    @property
    def max_workers(self) -> int:
        return self.get('analysis.max_workers', 4)
    
    @property
    def task_timeout(self) -> int:
        return self.get('analysis.task_timeout', 600)
    
    @property
    def similarity_threshold(self) -> float:
        return self.get('analysis.similarity_threshold', 0.8)
    
    @property
    def risk_threshold(self) -> float:
        return self.get('analysis.risk_threshold', 7.0)
    
    @property
    def jadx_path(self) -> str:
        return self.get('tools.jadx.path', 'jadx')
    
    @property
    def jadx_timeout(self) -> int:
        return self.get('tools.jadx.timeout', 300)
    
    @property
    def apktool_path(self) -> str:
        return self.get('tools.apktool.path', 'apktool')
    
    @property
    def apktool_timeout(self) -> int:
        return self.get('tools.apktool.timeout', 180)
    
    @property
    def max_file_size(self) -> int:
        return self.get('security.max_file_size', 104857600)
    
    @property
    def allowed_extensions(self) -> list:
        return self.get('security.allowed_extensions', ['.apk', '.APK'])
    
    def validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 检查必要的目录
            required_dirs = [self.data_dir, self.temp_dir, self.log_dir]
            for directory in required_dirs:
                if not os.path.exists(directory):
                    logging.warning(f"目录不存在: {directory}")
                    return False
            
            # 检查工具可用性
            tools = ['jadx', 'apktool']
            for tool in tools:
                tool_path = self.get(f'tools.{tool}.path')
                if not self._check_tool_available(tool_path):
                    logging.warning(f"工具不可用: {tool_path}")
            
            return True
            
        except Exception as e:
            logging.error(f"配置验证失败: {e}")
            return False
    
    def _check_tool_available(self, tool_path: str) -> bool:
        """检查外部工具是否可用"""
        try:
            import subprocess
            result = subprocess.run([tool_path, '--version'], 
                                  capture_output=True, timeout=10)
            return result.returncode == 0
        except:
            return False
