# Android APP Analytics

一个专业的Android APK安全分析工具，专注于Google Play应用审核和代码关联性分析。

## 🚀 项目概述

Android APP Analytics 是一个基于Python开发的APK静态分析工具，能够对APK文件进行多维度特征提取，通过与特征数据库比对评估应用的安全风险，并生成详尽的可视化分析报告。

### 核心功能

- **📱 APK基础信息提取**: 包名、版本、签名证书、SDK版本等
- **🔍 深度Manifest解析**: 权限、组件、Intent过滤器分析
- **💻 代码分析**: 使用Androguard和Jadx进行反编译和代码特征提取
- **🎨 资源分析**: 图标、布局、字符串资源的哈希计算
- **📚 原生库分析**: SO文件架构和ELF头信息提取
- **🌐 网络特征提取**: 域名和URL模式识别
- **⚠️ 风险评估**: 基于特征数据库的关联性风险评分
- **📊 可视化报告**: 生成HTML和JSON格式的详细分析报告

## 🏗️ 技术架构

### 核心技术栈
- **Python 3.8+**: 主要开发语言
- **Androguard**: APK解析和Dex分析
- **Jadx**: Java/Kotlin源代码反编译
- **SQLite**: 特征数据库存储
- **Jinja2**: HTML报告模板引擎
- **Bootstrap**: 前端UI框架

### 模块化设计
```
src/
├── core/           # 核心分析模块
├── database/       # 数据库管理
├── analysis/       # 风险评估算法
├── reporting/      # 报告生成
└── utils/          # 通用工具
```

## 📦 安装部署

### 环境要求
- Python 3.8 或更高版本
- 操作系统: Windows/Linux/macOS
- 内存: 建议4GB以上
- 存储: 至少1GB可用空间

### 快速安装
```bash
# 克隆项目
git clone https://github.com/your-repo/android-app-analytics.git
cd android-app-analytics

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python scripts/setup_database.py

# 运行测试
python -m pytest tests/
```

### 外部工具配置
```bash
# 安装Jadx (可选但推荐)
# 下载: https://github.com/skylot/jadx/releases
# 配置PATH环境变量

# 安装Android SDK工具 (可选)
# aapt工具用于APK基础信息解析
```

## 🎯 使用指南

### 命令行使用

#### 分析单个APK文件
```bash
python src/main.py -f app.apk -o ./reports
```

#### 批量分析APK文件
```bash
python src/main.py -d ./apk_files -o ./reports
```

#### 详细参数说明
```bash
python src/main.py --help

选项:
  -f, --file        单个APK文件路径
  -d, --directory   包含APK文件的目录
  -o, --output      输出目录
  -c, --config      配置文件路径
  -v, --verbose     详细输出模式
  --version         显示版本信息
```

### 配置文件

创建 `config.json` 自定义配置:
```json
{
  "analysis": {
    "max_workers": 4,
    "task_timeout": 600,
    "enable_jadx": true,
    "similarity_threshold": 0.8
  },
  "tools": {
    "jadx": {
      "path": "/path/to/jadx",
      "timeout": 300
    }
  },
  "security": {
    "max_file_size": 104857600
  }
}
```

## 📊 报告示例

### HTML报告特性
- 🎨 响应式设计，支持移动端查看
- 📈 交互式图表和统计数据
- 🔍 全文搜索和内容过滤
- 📋 一键复制哈希值和代码
- 🖨️ 支持打印和PDF导出

### 风险评分体系
- **0-2分**: 安全 (绿色)
- **2-4分**: 低风险 (蓝色)
- **4-6分**: 中等风险 (橙色)
- **6-8分**: 中高风险 (橙红色)
- **8-10分**: 高风险 (红色)

## 🔧 开发指南

### 项目结构
```
Android APP Analytics/
├── src/                    # 源代码
│   ├── core/              # 核心分析模块
│   ├── database/          # 数据库管理
│   ├── analysis/          # 分析算法
│   ├── reporting/         # 报告生成
│   └── utils/             # 工具类
├── config/                # 配置文件
├── templates/             # 报告模板
├── data/                  # 数据存储
├── tests/                 # 单元测试
├── scripts/               # 辅助脚本
└── docs/                  # 文档
```

### 扩展开发

#### 添加新的分析器
```python
# src/core/custom_analyzer.py
class CustomAnalyzer:
    def analyze(self, apk_path: str) -> Dict[str, Any]:
        # 实现自定义分析逻辑
        return analysis_result
```

#### 自定义风险评分算法
```python
# src/analysis/custom_risk_calculator.py
class CustomRiskCalculator(RiskCalculator):
    def calculate_custom_risk(self, features: Dict) -> float:
        # 实现自定义风险评分逻辑
        return risk_score
```

### 测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_apk_parser.py

# 生成覆盖率报告
python -m pytest --cov=src tests/
```

## 📈 性能优化

### 并行处理
- 支持多线程并行特征提取
- 可配置工作线程数量
- 智能任务调度和负载均衡

### 缓存机制
- 文件哈希缓存避免重复计算
- 分析结果缓存提升批量处理效率
- 可配置缓存策略和过期时间

### 内存管理
- 流式处理大文件避免内存溢出
- 及时清理临时文件和资源
- 内存使用监控和告警

## 🛡️ 安全考虑

### 文件安全
- 严格的APK文件格式验证
- 文件大小和类型限制
- 沙箱环境中执行分析

### 数据隐私
- 本地化处理，不上传敏感数据
- 可配置的数据保留策略
- 支持数据加密存储

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- 遵循 PEP 8 Python代码规范
- 使用 Black 进行代码格式化
- 添加适当的类型注解
- 编写单元测试覆盖新功能

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与反馈

- 🐛 **Bug报告**: [GitHub Issues](https://github.com/your-repo/android-app-analytics/issues)
- 💡 **功能建议**: [GitHub Discussions](https://github.com/your-repo/android-app-analytics/discussions)
- 📧 **邮件联系**: <EMAIL>

## 🙏 致谢

感谢以下开源项目的支持：
- [Androguard](https://github.com/androguard/androguard) - Android应用分析框架
- [Jadx](https://github.com/skylot/jadx) - Dex到Java反编译器
- [Bootstrap](https://getbootstrap.com/) - 前端UI框架
- [Chart.js](https://www.chartjs.org/) - 图表库

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
