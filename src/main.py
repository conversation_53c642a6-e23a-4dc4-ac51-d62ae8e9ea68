#!/usr/bin/env python3
"""
Android APP Analytics - 主程序入口
功能：协调整个APK分析工作流程
作者：Android安全工程师
"""

import argparse
import sys
import os
import logging
from pathlib import Path
from typing import List, Dict, Any
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import Settings
from config.logging_config import setup_logging
from src.core.apk_parser import APKParser
from src.core.feature_extractor import FeatureExtractor
from src.database.database_manager import DatabaseManager
from src.analysis.risk_calculator import RiskCalculator
from src.reporting.report_generator import ReportGenerator
from src.utils.validators import validate_apk_file


class AndroidAppAnalytics:
    """Android APP Analytics 主控制器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化分析器
        
        Args:
            config_path: 配置文件路径
        """
        self.settings = Settings(config_path)
        self.logger = setup_logging(self.settings.log_level, self.settings.log_file)
        
        # 初始化核心组件
        self.db_manager = DatabaseManager(self.settings.database_path)
        self.apk_parser = APKParser()
        self.feature_extractor = FeatureExtractor(self.settings)
        self.risk_calculator = RiskCalculator(self.db_manager)
        self.report_generator = ReportGenerator(self.settings.template_dir)
        
        self.logger.info("Android APP Analytics 初始化完成")
    
    def analyze_single_apk(self, apk_path: str, output_dir: str = None) -> Dict[str, Any]:
        """
        分析单个APK文件
        
        Args:
            apk_path: APK文件路径
            output_dir: 输出目录，默认为当前目录
            
        Returns:
            分析结果字典
        """
        try:
            self.logger.info(f"开始分析APK: {apk_path}")
            
            # 1. 验证APK文件
            if not validate_apk_file(apk_path):
                raise ValueError(f"无效的APK文件: {apk_path}")
            
            # 2. 解析APK基础信息
            apk_info = self.apk_parser.parse_apk(apk_path)
            self.logger.info(f"APK基础信息解析完成: {apk_info['package_name']}")
            
            # 3. 提取多维特征
            features = self.feature_extractor.extract_all_features(apk_path, apk_info)
            self.logger.info("特征提取完成")
            
            # 4. 存储到数据库
            apk_id = self.db_manager.store_apk_analysis(apk_info, features)
            self.logger.info(f"分析结果已存储到数据库，APK ID: {apk_id}")
            
            # 5. 计算风险评分
            risk_assessment = self.risk_calculator.calculate_risk_score(apk_id, features)
            self.db_manager.store_risk_assessment(apk_id, risk_assessment)
            self.logger.info(f"风险评分完成: {risk_assessment['overall_risk_score']:.2f}")
            
            # 6. 生成报告
            if output_dir is None:
                output_dir = os.path.dirname(apk_path)
            
            report_data = {
                'apk_info': apk_info,
                'features': features,
                'risk_assessment': risk_assessment,
                'analysis_metadata': {
                    'analysis_date': features.get('analysis_date'),
                    'tool_version': self.settings.version,
                    'apk_id': apk_id
                }
            }
            
            # 生成JSON报告
            json_report_path = self.report_generator.generate_json_report(
                report_data, output_dir, apk_info['package_name']
            )
            
            # 生成HTML报告
            html_report_path = self.report_generator.generate_html_report(
                report_data, output_dir, apk_info['package_name']
            )
            
            self.logger.info(f"报告生成完成:")
            self.logger.info(f"  JSON报告: {json_report_path}")
            self.logger.info(f"  HTML报告: {html_report_path}")
            
            return {
                'success': True,
                'apk_id': apk_id,
                'package_name': apk_info['package_name'],
                'risk_score': risk_assessment['overall_risk_score'],
                'json_report': json_report_path,
                'html_report': html_report_path,
                'analysis_summary': {
                    'permissions_count': len(features.get('permissions', [])),
                    'components_count': len(features.get('components', [])),
                    'network_features_count': len(features.get('network_features', [])),
                    'native_libraries_count': len(features.get('native_libraries', [])),
                    'high_risk_matches': len([m for m in risk_assessment.get('similarity_matches', []) 
                                            if m.get('risk_level', 0) >= 4])
                }
            }
            
        except Exception as e:
            self.logger.error(f"分析APK时发生错误: {str(e)}", exc_info=True)
            return {
                'success': False,
                'error': str(e),
                'apk_path': apk_path
            }
    
    def analyze_batch_apks(self, apk_paths: List[str], output_dir: str = None) -> List[Dict[str, Any]]:
        """
        批量分析多个APK文件
        
        Args:
            apk_paths: APK文件路径列表
            output_dir: 输出目录
            
        Returns:
            分析结果列表
        """
        results = []
        total_count = len(apk_paths)
        
        self.logger.info(f"开始批量分析 {total_count} 个APK文件")
        
        for i, apk_path in enumerate(apk_paths, 1):
            self.logger.info(f"处理进度: {i}/{total_count} - {apk_path}")
            result = self.analyze_single_apk(apk_path, output_dir)
            results.append(result)
        
        # 生成批量分析汇总报告
        if output_dir:
            summary_report = self._generate_batch_summary(results)
            summary_path = os.path.join(output_dir, "batch_analysis_summary.json")
            with open(summary_path, 'w', encoding='utf-8') as f:
                json.dump(summary_report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"批量分析汇总报告: {summary_path}")
        
        return results
    
    def _generate_batch_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成批量分析汇总报告"""
        successful = [r for r in results if r.get('success')]
        failed = [r for r in results if not r.get('success')]
        
        if successful:
            risk_scores = [r['risk_score'] for r in successful]
            avg_risk_score = sum(risk_scores) / len(risk_scores)
            high_risk_count = len([s for s in risk_scores if s >= 7.0])
        else:
            avg_risk_score = 0
            high_risk_count = 0
        
        return {
            'total_analyzed': len(results),
            'successful_count': len(successful),
            'failed_count': len(failed),
            'average_risk_score': avg_risk_score,
            'high_risk_count': high_risk_count,
            'failed_files': [r.get('apk_path') for r in failed],
            'analysis_timestamp': self.feature_extractor.get_current_timestamp()
        }


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(
        description='Android APP Analytics - APK安全分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 分析单个APK文件
  python main.py -f app.apk -o ./reports
  
  # 批量分析APK文件
  python main.py -d ./apk_files -o ./reports
  
  # 指定配置文件
  python main.py -f app.apk -c config.json
        """
    )
    
    parser.add_argument('-f', '--file', type=str, help='单个APK文件路径')
    parser.add_argument('-d', '--directory', type=str, help='包含APK文件的目录')
    parser.add_argument('-o', '--output', type=str, help='输出目录')
    parser.add_argument('-c', '--config', type=str, help='配置文件路径')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出模式')
    parser.add_argument('--version', action='version', version='Android APP Analytics v1.0.0')
    
    args = parser.parse_args()
    
    # 参数验证
    if not args.file and not args.directory:
        parser.error("必须指定 -f (单个文件) 或 -d (目录) 参数")
    
    if args.file and args.directory:
        parser.error("不能同时指定 -f 和 -d 参数")
    
    try:
        # 初始化分析器
        analyzer = AndroidAppAnalytics(args.config)
        
        if args.verbose:
            analyzer.logger.setLevel(logging.DEBUG)
        
        # 执行分析
        if args.file:
            # 单文件分析
            result = analyzer.analyze_single_apk(args.file, args.output)
            if result['success']:
                print(f"✅ 分析完成: {result['package_name']}")
                print(f"   风险评分: {result['risk_score']:.2f}/10")
                print(f"   HTML报告: {result['html_report']}")
            else:
                print(f"❌ 分析失败: {result['error']}")
                sys.exit(1)
        
        elif args.directory:
            # 批量分析
            apk_files = []
            for ext in ['*.apk', '*.APK']:
                apk_files.extend(Path(args.directory).glob(ext))
            
            if not apk_files:
                print(f"❌ 在目录 {args.directory} 中未找到APK文件")
                sys.exit(1)
            
            apk_paths = [str(f) for f in apk_files]
            results = analyzer.analyze_batch_apks(apk_paths, args.output)
            
            successful = len([r for r in results if r.get('success')])
            total = len(results)
            print(f"✅ 批量分析完成: {successful}/{total} 个文件成功")
    
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
