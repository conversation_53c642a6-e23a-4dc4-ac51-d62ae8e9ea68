"""
APK解析器 - 负责APK文件验证和基础信息提取
"""

import os
import logging
import hashlib
from typing import Dict, Any, Optional
from pathlib import Path

try:
    from androguard.core.bytecodes.apk import APK
    from androguard.core.bytecodes.axml import AXML
except ImportError:
    logging.warning("Androguard未安装，某些功能可能不可用")
    APK = None
    AXML = None

from ..utils.file_utils import FileUtils
from ..utils.hash_utils import HashUtils
from ..utils.validators import validate_apk_file


class APKParser:
    """APK文件解析器"""
    
    def __init__(self):
        """初始化APK解析器"""
        self.logger = logging.getLogger(__name__)
        self.file_utils = FileUtils()
        self.hash_utils = HashUtils()
        
        if APK is None:
            self.logger.warning("Androguard不可用，将使用基础解析功能")
    
    def parse_apk(self, apk_path: str) -> Dict[str, Any]:
        """
        解析APK文件，提取基础信息
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            包含APK基础信息的字典
        """
        try:
            self.logger.info(f"开始解析APK: {apk_path}")
            
            # 验证APK文件
            if not validate_apk_file(apk_path):
                raise ValueError(f"无效的APK文件: {apk_path}")
            
            # 获取文件基础信息
            file_info = self._get_file_info(apk_path)
            
            # 使用Androguard解析APK
            if APK is not None:
                apk_info = self._parse_with_androguard(apk_path)
            else:
                apk_info = self._parse_basic_info(apk_path)
            
            # 合并信息
            result = {**file_info, **apk_info}
            
            self.logger.info(f"APK解析完成: {result.get('package_name', 'unknown')}")
            return result
            
        except Exception as e:
            self.logger.error(f"APK解析失败: {str(e)}")
            raise
    
    def _get_file_info(self, apk_path: str) -> Dict[str, Any]:
        """获取文件基础信息"""
        file_path = Path(apk_path)
        file_size = file_path.stat().st_size
        
        # 计算文件哈希
        file_hashes = self.hash_utils.calculate_file_hashes(apk_path)
        
        return {
            'file_path': str(file_path.absolute()),
            'file_name': file_path.name,
            'file_size': file_size,
            'file_md5': file_hashes['md5'],
            'file_sha1': file_hashes['sha1'],
            'file_sha256': file_hashes['sha256']
        }
    
    def _parse_with_androguard(self, apk_path: str) -> Dict[str, Any]:
        """使用Androguard解析APK"""
        try:
            apk = APK(apk_path)
            
            # 提取基础信息
            apk_info = {
                'package_name': apk.get_package(),
                'version_name': apk.get_androidversion_name(),
                'version_code': apk.get_androidversion_code(),
                'target_sdk_version': apk.get_target_sdk_version(),
                'min_sdk_version': apk.get_min_sdk_version(),
                'compile_sdk_version': apk.get_compile_sdk_version(),
                'app_name': apk.get_app_name(),
                'app_icon': apk.get_app_icon(),
                'is_signed': apk.is_signed(),
                'is_valid_apk': apk.is_valid_APK()
            }
            
            # 获取权限信息
            permissions = apk.get_permissions()
            apk_info['permissions_count'] = len(permissions) if permissions else 0
            
            # 获取Activities信息
            activities = apk.get_activities()
            apk_info['activities_count'] = len(activities) if activities else 0
            
            # 获取Services信息
            services = apk.get_services()
            apk_info['services_count'] = len(services) if services else 0
            
            # 获取Receivers信息
            receivers = apk.get_receivers()
            apk_info['receivers_count'] = len(receivers) if receivers else 0
            
            # 获取Providers信息
            providers = apk.get_providers()
            apk_info['providers_count'] = len(providers) if providers else 0
            
            return apk_info
            
        except Exception as e:
            self.logger.error(f"Androguard解析失败: {str(e)}")
            return self._parse_basic_info(apk_path)
    
    def _parse_basic_info(self, apk_path: str) -> Dict[str, Any]:
        """基础APK信息解析（不依赖Androguard）"""
        try:
            # 使用aapt工具解析（如果可用）
            aapt_info = self._parse_with_aapt(apk_path)
            if aapt_info:
                return aapt_info
            
            # 使用ZIP解析作为后备方案
            return self._parse_as_zip(apk_path)
            
        except Exception as e:
            self.logger.error(f"基础解析失败: {str(e)}")
            return {
                'package_name': 'unknown',
                'version_name': 'unknown',
                'version_code': 0,
                'target_sdk_version': 0,
                'min_sdk_version': 0,
                'compile_sdk_version': 0,
                'app_name': 'unknown',
                'is_signed': False,
                'is_valid_apk': False
            }
    
    def _parse_with_aapt(self, apk_path: str) -> Optional[Dict[str, Any]]:
        """使用aapt工具解析APK"""
        try:
            import subprocess
            
            # 执行aapt dump badging命令
            result = subprocess.run(
                ['aapt', 'dump', 'badging', apk_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                return None
            
            # 解析aapt输出
            return self._parse_aapt_output(result.stdout)
            
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            self.logger.warning("aapt工具不可用或执行失败")
            return None
    
    def _parse_aapt_output(self, aapt_output: str) -> Dict[str, Any]:
        """解析aapt输出"""
        apk_info = {
            'package_name': 'unknown',
            'version_name': 'unknown',
            'version_code': 0,
            'target_sdk_version': 0,
            'min_sdk_version': 0,
            'compile_sdk_version': 0,
            'app_name': 'unknown',
            'is_signed': False,
            'is_valid_apk': True
        }
        
        lines = aapt_output.split('\n')
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('package:'):
                # 解析包信息
                import re
                
                # 提取包名
                package_match = re.search(r"name='([^']+)'", line)
                if package_match:
                    apk_info['package_name'] = package_match.group(1)
                
                # 提取版本代码
                version_code_match = re.search(r"versionCode='([^']+)'", line)
                if version_code_match:
                    try:
                        apk_info['version_code'] = int(version_code_match.group(1))
                    except ValueError:
                        pass
                
                # 提取版本名称
                version_name_match = re.search(r"versionName='([^']+)'", line)
                if version_name_match:
                    apk_info['version_name'] = version_name_match.group(1)
            
            elif line.startswith('sdkVersion:'):
                # 最小SDK版本
                try:
                    apk_info['min_sdk_version'] = int(line.split(':')[1].strip().strip("'"))
                except (ValueError, IndexError):
                    pass
            
            elif line.startswith('targetSdkVersion:'):
                # 目标SDK版本
                try:
                    apk_info['target_sdk_version'] = int(line.split(':')[1].strip().strip("'"))
                except (ValueError, IndexError):
                    pass
            
            elif line.startswith('application-label:'):
                # 应用名称
                try:
                    apk_info['app_name'] = line.split(':', 1)[1].strip().strip("'")
                except IndexError:
                    pass
        
        return apk_info
    
    def _parse_as_zip(self, apk_path: str) -> Dict[str, Any]:
        """将APK作为ZIP文件解析"""
        try:
            import zipfile
            
            apk_info = {
                'package_name': 'unknown',
                'version_name': 'unknown',
                'version_code': 0,
                'target_sdk_version': 0,
                'min_sdk_version': 0,
                'compile_sdk_version': 0,
                'app_name': 'unknown',
                'is_signed': False,
                'is_valid_apk': True
            }
            
            with zipfile.ZipFile(apk_path, 'r') as zip_file:
                # 检查APK结构
                file_list = zip_file.namelist()
                
                # 检查必要文件
                has_manifest = 'AndroidManifest.xml' in file_list
                has_classes = any(f.startswith('classes') and f.endswith('.dex') for f in file_list)
                has_resources = 'resources.arsc' in file_list
                
                apk_info['is_valid_apk'] = has_manifest and has_classes
                
                # 检查签名
                has_meta_inf = any(f.startswith('META-INF/') for f in file_list)
                has_cert = any(f.endswith('.RSA') or f.endswith('.DSA') for f in file_list)
                apk_info['is_signed'] = has_meta_inf and has_cert
                
                # 尝试读取AndroidManifest.xml（二进制格式）
                if has_manifest:
                    try:
                        manifest_data = zip_file.read('AndroidManifest.xml')
                        # 这里需要解析二进制XML，但不依赖外部库的话比较复杂
                        # 暂时跳过详细解析
                    except Exception:
                        pass
            
            return apk_info
            
        except Exception as e:
            self.logger.error(f"ZIP解析失败: {str(e)}")
            raise
    
    def extract_manifest_xml(self, apk_path: str) -> Optional[str]:
        """
        提取AndroidManifest.xml内容
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            Manifest XML内容，如果提取失败返回None
        """
        try:
            if APK is not None:
                apk = APK(apk_path)
                return apk.get_android_manifest_xml()
            else:
                self.logger.warning("Androguard不可用，无法提取Manifest XML")
                return None
                
        except Exception as e:
            self.logger.error(f"提取Manifest XML失败: {str(e)}")
            return None
    
    def get_apk_icon(self, apk_path: str) -> Optional[bytes]:
        """
        获取APK图标数据
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            图标二进制数据，如果获取失败返回None
        """
        try:
            if APK is not None:
                apk = APK(apk_path)
                icon_path = apk.get_app_icon()
                if icon_path:
                    return apk.get_file(icon_path)
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取APK图标失败: {str(e)}")
            return None
    
    def validate_apk_structure(self, apk_path: str) -> Dict[str, bool]:
        """
        验证APK文件结构
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            验证结果字典
        """
        try:
            import zipfile
            
            validation_result = {
                'is_valid_zip': False,
                'has_manifest': False,
                'has_classes_dex': False,
                'has_resources': False,
                'has_signature': False,
                'is_valid_apk': False
            }
            
            # 检查是否为有效的ZIP文件
            try:
                with zipfile.ZipFile(apk_path, 'r') as zip_file:
                    validation_result['is_valid_zip'] = True
                    file_list = zip_file.namelist()
                    
                    # 检查必要文件
                    validation_result['has_manifest'] = 'AndroidManifest.xml' in file_list
                    validation_result['has_classes_dex'] = any(
                        f.startswith('classes') and f.endswith('.dex') for f in file_list
                    )
                    validation_result['has_resources'] = 'resources.arsc' in file_list
                    validation_result['has_signature'] = any(
                        f.startswith('META-INF/') and (f.endswith('.RSA') or f.endswith('.DSA'))
                        for f in file_list
                    )
                    
                    # 综合判断
                    validation_result['is_valid_apk'] = (
                        validation_result['has_manifest'] and 
                        validation_result['has_classes_dex']
                    )
                    
            except zipfile.BadZipFile:
                pass
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"APK结构验证失败: {str(e)}")
            return {key: False for key in ['is_valid_zip', 'has_manifest', 'has_classes_dex', 
                                         'has_resources', 'has_signature', 'is_valid_apk']}
