"""
特征提取器 - 协调各个分析模块提取APK的多维特征
"""

import os
import logging
import hashlib
from datetime import datetime
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed

from .manifest_analyzer import ManifestAnalyzer
from .code_analyzer import CodeAnalyzer
from .resource_analyzer import ResourceAnalyzer
from .native_analyzer import NativeAnalyzer
from .network_analyzer import NetworkAnalyzer
from ..utils.hash_utils import HashUtils
from ..utils.file_utils import FileUtils


class FeatureExtractor:
    """APK特征提取主控制器"""
    
    def __init__(self, settings):
        """
        初始化特征提取器
        
        Args:
            settings: 全局配置对象
        """
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个分析器
        self.manifest_analyzer = ManifestAnalyzer()
        self.code_analyzer = CodeAnalyzer(settings)
        self.resource_analyzer = ResourceAnalyzer()
        self.native_analyzer = NativeAnalyzer()
        self.network_analyzer = NetworkAnalyzer()
        
        # 工具类
        self.hash_utils = HashUtils()
        self.file_utils = FileUtils()
        
        self.logger.info("特征提取器初始化完成")
    
    def extract_all_features(self, apk_path: str, apk_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取APK的所有特征
        
        Args:
            apk_path: APK文件路径
            apk_info: APK基础信息
            
        Returns:
            包含所有特征的字典
        """
        self.logger.info(f"开始提取APK特征: {apk_path}")
        
        features = {
            'analysis_date': self.get_current_timestamp(),
            'apk_path': apk_path,
            'package_name': apk_info.get('package_name'),
        }
        
        try:
            # 创建临时工作目录
            temp_dir = self._create_temp_directory(apk_info['package_name'])
            
            # 并行执行特征提取任务
            extraction_tasks = [
                ('manifest_features', self._extract_manifest_features, apk_path),
                ('certificate_features', self._extract_certificate_features, apk_path),
                ('code_features', self._extract_code_features, apk_path, temp_dir),
                ('resource_features', self._extract_resource_features, apk_path, temp_dir),
                ('native_features', self._extract_native_features, apk_path),
                ('network_features', self._extract_network_features, apk_path, temp_dir),
            ]
            
            # 使用线程池并行执行
            with ThreadPoolExecutor(max_workers=self.settings.max_workers) as executor:
                future_to_task = {
                    executor.submit(task_func, *task_args): task_name 
                    for task_name, task_func, *task_args in extraction_tasks
                }
                
                for future in as_completed(future_to_task):
                    task_name = future_to_task[future]
                    try:
                        result = future.result(timeout=self.settings.task_timeout)
                        features[task_name] = result
                        self.logger.info(f"✅ {task_name} 提取完成")
                    except Exception as e:
                        self.logger.error(f"❌ {task_name} 提取失败: {str(e)}")
                        features[task_name] = {}
            
            # 清理临时目录
            self.file_utils.cleanup_directory(temp_dir)
            
            # 计算综合特征哈希
            features['composite_hash'] = self._calculate_composite_hash(features)
            
            self.logger.info("APK特征提取完成")
            return features
            
        except Exception as e:
            self.logger.error(f"特征提取过程中发生错误: {str(e)}", exc_info=True)
            raise
    
    def _extract_manifest_features(self, apk_path: str) -> Dict[str, Any]:
        """提取Manifest特征"""
        try:
            return self.manifest_analyzer.analyze_manifest(apk_path)
        except Exception as e:
            self.logger.error(f"Manifest分析失败: {str(e)}")
            return {}
    
    def _extract_certificate_features(self, apk_path: str) -> Dict[str, Any]:
        """提取证书特征"""
        try:
            return self.manifest_analyzer.extract_certificate_info(apk_path)
        except Exception as e:
            self.logger.error(f"证书分析失败: {str(e)}")
            return {}
    
    def _extract_code_features(self, apk_path: str, temp_dir: str) -> Dict[str, Any]:
        """提取代码特征"""
        try:
            # 首先使用Androguard进行基础分析
            androguard_features = self.code_analyzer.analyze_with_androguard(apk_path)
            
            # 然后使用Jadx进行反编译分析
            jadx_features = self.code_analyzer.analyze_with_jadx(apk_path, temp_dir)
            
            # 合并分析结果
            code_features = {
                **androguard_features,
                **jadx_features,
                'analysis_methods': ['androguard', 'jadx']
            }
            
            return code_features
        except Exception as e:
            self.logger.error(f"代码分析失败: {str(e)}")
            return {}
    
    def _extract_resource_features(self, apk_path: str, temp_dir: str) -> Dict[str, Any]:
        """提取资源特征"""
        try:
            return self.resource_analyzer.analyze_resources(apk_path, temp_dir)
        except Exception as e:
            self.logger.error(f"资源分析失败: {str(e)}")
            return {}
    
    def _extract_native_features(self, apk_path: str) -> Dict[str, Any]:
        """提取原生库特征"""
        try:
            return self.native_analyzer.analyze_native_libraries(apk_path)
        except Exception as e:
            self.logger.error(f"原生库分析失败: {str(e)}")
            return {}
    
    def _extract_network_features(self, apk_path: str, temp_dir: str) -> Dict[str, Any]:
        """提取网络特征"""
        try:
            return self.network_analyzer.extract_network_features(apk_path, temp_dir)
        except Exception as e:
            self.logger.error(f"网络特征分析失败: {str(e)}")
            return {}
    
    def _create_temp_directory(self, package_name: str) -> str:
        """创建临时工作目录"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        temp_dir = os.path.join(
            self.settings.temp_dir, 
            f"{package_name}_{timestamp}"
        )
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir
    
    def _calculate_composite_hash(self, features: Dict[str, Any]) -> str:
        """
        计算综合特征哈希值，用于快速相似度比较
        
        Args:
            features: 提取的特征字典
            
        Returns:
            综合特征的SHA256哈希值
        """
        try:
            # 选择关键特征进行哈希计算
            key_features = {
                'package_name': features.get('package_name', ''),
                'permissions': sorted(features.get('manifest_features', {}).get('permissions', [])),
                'activities': sorted(features.get('manifest_features', {}).get('activities', [])),
                'services': sorted(features.get('manifest_features', {}).get('services', [])),
                'receivers': sorted(features.get('manifest_features', {}).get('receivers', [])),
                'cert_hash': features.get('certificate_features', {}).get('cert_sha256', ''),
                'class_names_hash': features.get('code_features', {}).get('class_names_hash', ''),
                'strings_hash': features.get('code_features', {}).get('strings_hash', ''),
                'icon_hash': features.get('resource_features', {}).get('icon_hash', ''),
                'native_libs': sorted([
                    lib.get('library_name', '') 
                    for lib in features.get('native_features', {}).get('libraries', [])
                ]),
                'domains': sorted([
                    nf.get('domain_name', '') 
                    for nf in features.get('network_features', {}).get('domains', [])
                ])
            }
            
            # 序列化并计算哈希
            import json
            features_str = json.dumps(key_features, sort_keys=True, ensure_ascii=False)
            return hashlib.sha256(features_str.encode('utf-8')).hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算综合哈希失败: {str(e)}")
            return ""
    
    def get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.now().isoformat()
    
    def extract_quick_features(self, apk_path: str) -> Dict[str, Any]:
        """
        快速特征提取（仅提取基础特征，用于快速筛选）
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            基础特征字典
        """
        try:
            self.logger.info(f"执行快速特征提取: {apk_path}")
            
            # 仅提取关键特征
            quick_features = {
                'analysis_date': self.get_current_timestamp(),
                'apk_path': apk_path,
            }
            
            # 基础文件信息
            file_info = self.file_utils.get_file_info(apk_path)
            quick_features['file_info'] = file_info
            
            # Manifest基础信息
            manifest_basic = self.manifest_analyzer.extract_basic_info(apk_path)
            quick_features['manifest_basic'] = manifest_basic
            
            # 证书信息
            cert_info = self.manifest_analyzer.extract_certificate_info(apk_path)
            quick_features['certificate_info'] = cert_info
            
            return quick_features
            
        except Exception as e:
            self.logger.error(f"快速特征提取失败: {str(e)}")
            return {}
    
    def validate_extraction_result(self, features: Dict[str, Any]) -> bool:
        """
        验证特征提取结果的完整性
        
        Args:
            features: 提取的特征字典
            
        Returns:
            验证是否通过
        """
        required_sections = [
            'manifest_features',
            'certificate_features', 
            'code_features',
            'resource_features'
        ]
        
        for section in required_sections:
            if section not in features or not features[section]:
                self.logger.warning(f"缺少必要的特征部分: {section}")
                return False
        
        return True
