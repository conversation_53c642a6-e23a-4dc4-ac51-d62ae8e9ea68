"""
数据验证工具
"""

import os
import logging
import magic
from pathlib import Path
from typing import List, Optional


def validate_apk_file(file_path: str) -> bool:
    """
    验证APK文件的有效性
    
    Args:
        file_path: APK文件路径
        
    Returns:
        是否为有效的APK文件
    """
    logger = logging.getLogger(__name__)
    
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return False
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        if file_size == 0:
            logger.error(f"文件大小为0: {file_path}")
            return False
        
        # 检查文件扩展名
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in ['.apk']:
            logger.warning(f"文件扩展名不是.apk: {file_path}")
        
        # 使用python-magic检查文件类型
        try:
            file_type = magic.from_file(file_path)
            if 'zip' not in file_type.lower() and 'archive' not in file_type.lower():
                logger.warning(f"文件类型可能不是ZIP/APK: {file_type}")
        except Exception as e:
            logger.warning(f"无法检测文件类型: {str(e)}")
        
        # 尝试作为ZIP文件打开
        try:
            import zipfile
            with zipfile.ZipFile(file_path, 'r') as zip_file:
                # 检查是否包含AndroidManifest.xml
                if 'AndroidManifest.xml' not in zip_file.namelist():
                    logger.error(f"APK文件缺少AndroidManifest.xml: {file_path}")
                    return False
                
                # 检查是否包含classes.dex
                has_dex = any(f.startswith('classes') and f.endswith('.dex') 
                             for f in zip_file.namelist())
                if not has_dex:
                    logger.error(f"APK文件缺少classes.dex: {file_path}")
                    return False
                
        except zipfile.BadZipFile:
            logger.error(f"文件不是有效的ZIP格式: {file_path}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"验证APK文件时发生错误: {str(e)}")
        return False


def validate_file_size(file_path: str, max_size: int = 100 * 1024 * 1024) -> bool:
    """
    验证文件大小
    
    Args:
        file_path: 文件路径
        max_size: 最大文件大小（字节），默认100MB
        
    Returns:
        文件大小是否在允许范围内
    """
    try:
        file_size = os.path.getsize(file_path)
        return file_size <= max_size
    except Exception:
        return False


def validate_package_name(package_name: str) -> bool:
    """
    验证Android包名格式
    
    Args:
        package_name: 包名
        
    Returns:
        包名格式是否有效
    """
    if not package_name:
        return False
    
    import re
    
    # Android包名规则：
    # 1. 至少包含一个点
    # 2. 每个部分以字母开头
    # 3. 只能包含字母、数字、下划线
    # 4. 不能以数字开头
    pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)+$'
    
    return bool(re.match(pattern, package_name))


def validate_version_code(version_code: any) -> bool:
    """
    验证版本代码
    
    Args:
        version_code: 版本代码
        
    Returns:
        版本代码是否有效
    """
    try:
        code = int(version_code)
        return code >= 0
    except (ValueError, TypeError):
        return False


def validate_sdk_version(sdk_version: any) -> bool:
    """
    验证SDK版本号
    
    Args:
        sdk_version: SDK版本号
        
    Returns:
        SDK版本号是否有效
    """
    try:
        version = int(sdk_version)
        return 1 <= version <= 35  # Android API级别范围
    except (ValueError, TypeError):
        return False


def validate_permission_name(permission: str) -> bool:
    """
    验证权限名称格式
    
    Args:
        permission: 权限名称
        
    Returns:
        权限名称格式是否有效
    """
    if not permission:
        return False
    
    import re
    
    # Android权限名称规则：
    # 1. 通常以android.permission.开头（系统权限）
    # 2. 或者是自定义权限（包名格式）
    system_permission_pattern = r'^android\.permission\.[A-Z_]+$'
    custom_permission_pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*$'
    
    return (bool(re.match(system_permission_pattern, permission)) or 
            bool(re.match(custom_permission_pattern, permission)))


def validate_component_name(component_name: str) -> bool:
    """
    验证组件名称格式
    
    Args:
        component_name: 组件名称
        
    Returns:
        组件名称格式是否有效
    """
    if not component_name:
        return False
    
    # 组件名称可以是：
    # 1. 完整类名（包含包名）
    # 2. 相对类名（以.开头）
    # 3. 简单类名
    
    import re
    
    # 完整类名
    full_class_pattern = r'^[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*$'
    # 相对类名
    relative_class_pattern = r'^\.[a-zA-Z][a-zA-Z0-9_]*(\.[a-zA-Z][a-zA-Z0-9_]*)*$'
    
    return (bool(re.match(full_class_pattern, component_name)) or 
            bool(re.match(relative_class_pattern, component_name)))


def validate_url(url: str) -> bool:
    """
    验证URL格式
    
    Args:
        url: URL字符串
        
    Returns:
        URL格式是否有效
    """
    if not url:
        return False
    
    import re
    
    # 简单的URL验证
    url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
    return bool(re.match(url_pattern, url))


def validate_domain(domain: str) -> bool:
    """
    验证域名格式
    
    Args:
        domain: 域名
        
    Returns:
        域名格式是否有效
    """
    if not domain:
        return False
    
    import re
    
    # 域名验证规则
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    
    return bool(re.match(domain_pattern, domain)) and len(domain) <= 253


def validate_hash(hash_value: str, hash_type: str) -> bool:
    """
    验证哈希值格式
    
    Args:
        hash_value: 哈希值
        hash_type: 哈希类型 (md5, sha1, sha256)
        
    Returns:
        哈希值格式是否有效
    """
    if not hash_value:
        return False
    
    import re
    
    hash_patterns = {
        'md5': r'^[a-fA-F0-9]{32}$',
        'sha1': r'^[a-fA-F0-9]{40}$',
        'sha256': r'^[a-fA-F0-9]{64}$'
    }
    
    pattern = hash_patterns.get(hash_type.lower())
    if not pattern:
        return False
    
    return bool(re.match(pattern, hash_value))


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        清理后的安全文件名
    """
    import re
    
    # 移除或替换不安全字符
    unsafe_chars = r'[<>:"/\\|?*\x00-\x1f]'
    safe_filename = re.sub(unsafe_chars, '_', filename)
    
    # 移除前后空格和点
    safe_filename = safe_filename.strip(' .')
    
    # 确保文件名不为空
    if not safe_filename:
        safe_filename = 'unnamed'
    
    # 限制文件名长度
    if len(safe_filename) > 255:
        safe_filename = safe_filename[:255]
    
    return safe_filename


def validate_analysis_config(config: dict) -> List[str]:
    """
    验证分析配置的有效性
    
    Args:
        config: 配置字典
        
    Returns:
        验证错误列表，空列表表示验证通过
    """
    errors = []
    
    # 检查必要的配置项
    required_keys = ['database_path', 'temp_dir', 'max_workers']
    for key in required_keys:
        if key not in config:
            errors.append(f"缺少必要配置项: {key}")
    
    # 验证数值配置
    if 'max_workers' in config:
        try:
            workers = int(config['max_workers'])
            if workers <= 0 or workers > 32:
                errors.append("max_workers必须在1-32之间")
        except (ValueError, TypeError):
            errors.append("max_workers必须是有效的整数")
    
    if 'task_timeout' in config:
        try:
            timeout = int(config['task_timeout'])
            if timeout <= 0:
                errors.append("task_timeout必须大于0")
        except (ValueError, TypeError):
            errors.append("task_timeout必须是有效的整数")
    
    # 验证路径配置
    path_keys = ['database_path', 'temp_dir', 'template_dir']
    for key in path_keys:
        if key in config:
            path = config[key]
            if not isinstance(path, str) or not path.strip():
                errors.append(f"{key}必须是有效的路径字符串")
    
    return errors
