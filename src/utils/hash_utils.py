"""
哈希计算工具类
"""

import hashlib
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class HashUtils:
    """哈希计算工具类"""
    
    def __init__(self):
        """初始化哈希工具"""
        self.logger = logging.getLogger(__name__)
    
    def calculate_file_hashes(self, file_path: str) -> Dict[str, str]:
        """
        计算文件的多种哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            包含MD5、SHA1、SHA256哈希值的字典
        """
        try:
            hashes = {
                'md5': hashlib.md5(),
                'sha1': hashlib.sha1(),
                'sha256': hashlib.sha256()
            }
            
            with open(file_path, 'rb') as f:
                # 分块读取文件以处理大文件
                while chunk := f.read(8192):
                    for hash_obj in hashes.values():
                        hash_obj.update(chunk)
            
            return {
                'md5': hashes['md5'].hexdigest(),
                'sha1': hashes['sha1'].hexdigest(),
                'sha256': hashes['sha256'].hexdigest()
            }
            
        except Exception as e:
            self.logger.error(f"计算文件哈希失败: {str(e)}")
            return {'md5': '', 'sha1': '', 'sha256': ''}
    
    def calculate_string_hash(self, text: str, algorithm: str = 'sha256') -> str:
        """
        计算字符串哈希值
        
        Args:
            text: 输入字符串
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            哈希值字符串
        """
        try:
            if algorithm == 'md5':
                hash_obj = hashlib.md5()
            elif algorithm == 'sha1':
                hash_obj = hashlib.sha1()
            elif algorithm == 'sha256':
                hash_obj = hashlib.sha256()
            else:
                raise ValueError(f"不支持的哈希算法: {algorithm}")
            
            hash_obj.update(text.encode('utf-8'))
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"计算字符串哈希失败: {str(e)}")
            return ""
    
    def calculate_list_hash(self, items: list, algorithm: str = 'sha256') -> str:
        """
        计算列表内容的哈希值
        
        Args:
            items: 输入列表
            algorithm: 哈希算法
            
        Returns:
            哈希值字符串
        """
        try:
            # 将列表转换为排序后的字符串
            sorted_items = sorted([str(item) for item in items])
            combined_string = '|'.join(sorted_items)
            return self.calculate_string_hash(combined_string, algorithm)
            
        except Exception as e:
            self.logger.error(f"计算列表哈希失败: {str(e)}")
            return ""
    
    def calculate_image_phash(self, image_data: bytes) -> Optional[str]:
        """
        计算图像的感知哈希值
        
        Args:
            image_data: 图像二进制数据
            
        Returns:
            感知哈希值字符串，失败返回None
        """
        try:
            from PIL import Image
            import imagehash
            import io
            
            # 从二进制数据创建图像对象
            image = Image.open(io.BytesIO(image_data))
            
            # 计算感知哈希
            phash = imagehash.phash(image)
            return str(phash)
            
        except ImportError:
            self.logger.warning("PIL或imagehash库不可用，无法计算感知哈希")
            return None
        except Exception as e:
            self.logger.error(f"计算感知哈希失败: {str(e)}")
            return None
    
    def calculate_fuzzy_hash(self, file_path: str) -> Optional[str]:
        """
        计算文件的模糊哈希值（ssdeep）
        
        Args:
            file_path: 文件路径
            
        Returns:
            模糊哈希值字符串，失败返回None
        """
        try:
            import ssdeep
            
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            return ssdeep.hash(file_data)
            
        except ImportError:
            self.logger.warning("ssdeep库不可用，无法计算模糊哈希")
            return None
        except Exception as e:
            self.logger.error(f"计算模糊哈希失败: {str(e)}")
            return None
    
    def compare_fuzzy_hashes(self, hash1: str, hash2: str) -> int:
        """
        比较两个模糊哈希的相似度
        
        Args:
            hash1: 第一个模糊哈希
            hash2: 第二个模糊哈希
            
        Returns:
            相似度分数 (0-100)
        """
        try:
            import ssdeep
            return ssdeep.compare(hash1, hash2)
            
        except ImportError:
            self.logger.warning("ssdeep库不可用，无法比较模糊哈希")
            return 0
        except Exception as e:
            self.logger.error(f"比较模糊哈希失败: {str(e)}")
            return 0
