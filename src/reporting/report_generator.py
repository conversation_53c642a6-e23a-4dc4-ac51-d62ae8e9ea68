"""
报告生成器 - 生成JSON和HTML格式的分析报告
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

from jinja2 import Environment, FileSystemLoader, select_autoescape
from .json_formatter import J<PERSON>NFormatter
from .html_formatter import HTMLFormatter


class ReportGenerator:
    """分析报告生成器"""
    
    def __init__(self, template_dir: str):
        """
        初始化报告生成器
        
        Args:
            template_dir: 模板文件目录
        """
        self.template_dir = template_dir
        self.logger = logging.getLogger(__name__)
        
        # 初始化Jinja2环境
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 注册自定义过滤器
        self._register_filters()
        
        # 初始化格式化器
        self.json_formatter = JSONFormatter()
        self.html_formatter = HTMLFormatter()
        
        self.logger.info("报告生成器初始化完成")
    
    def _register_filters(self):
        """注册Jinja2自定义过滤器"""
        
        @self.jinja_env.filter('risk_level_color')
        def risk_level_color(score: float) -> str:
            """根据风险评分返回颜色"""
            if score >= 8.0:
                return 'danger'  # 红色
            elif score >= 6.0:
                return 'warning'  # 橙色
            elif score >= 4.0:
                return 'info'  # 蓝色
            else:
                return 'success'  # 绿色
        
        @self.jinja_env.filter('risk_level_text')
        def risk_level_text(score: float) -> str:
            """根据风险评分返回文本描述"""
            if score >= 8.0:
                return '高风险'
            elif score >= 6.0:
                return '中高风险'
            elif score >= 4.0:
                return '中等风险'
            elif score >= 2.0:
                return '低风险'
            else:
                return '安全'
        
        @self.jinja_env.filter('format_timestamp')
        def format_timestamp(timestamp: str) -> str:
            """格式化时间戳"""
            try:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            except:
                return timestamp
        
        @self.jinja_env.filter('format_file_size')
        def format_file_size(size_bytes: int) -> str:
            """格式化文件大小"""
            if size_bytes == 0:
                return "0 B"
            
            size_names = ["B", "KB", "MB", "GB"]
            i = 0
            while size_bytes >= 1024 and i < len(size_names) - 1:
                size_bytes /= 1024.0
                i += 1
            
            return f"{size_bytes:.1f} {size_names[i]}"
        
        @self.jinja_env.filter('truncate_text')
        def truncate_text(text: str, length: int = 50) -> str:
            """截断文本"""
            if len(text) <= length:
                return text
            return text[:length] + "..."
    
    def generate_json_report(self, report_data: Dict[str, Any], output_dir: str, 
                           package_name: str) -> str:
        """
        生成JSON格式报告
        
        Args:
            report_data: 报告数据
            output_dir: 输出目录
            package_name: 应用包名
            
        Returns:
            生成的JSON报告文件路径
        """
        try:
            # 格式化报告数据
            formatted_data = self.json_formatter.format_report_data(report_data)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{package_name}_{timestamp}_report.json"
            output_path = os.path.join(output_dir, filename)
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 写入JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(formatted_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"JSON报告已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成JSON报告失败: {str(e)}")
            raise
    
    def generate_html_report(self, report_data: Dict[str, Any], output_dir: str, 
                           package_name: str) -> str:
        """
        生成HTML格式报告
        
        Args:
            report_data: 报告数据
            output_dir: 输出目录
            package_name: 应用包名
            
        Returns:
            生成的HTML报告文件路径
        """
        try:
            # 格式化报告数据
            formatted_data = self.html_formatter.format_report_data(report_data)
            
            # 加载HTML模板
            template = self.jinja_env.get_template('report_template.html')
            
            # 渲染HTML内容
            html_content = template.render(
                report_data=formatted_data,
                generation_time=datetime.now().isoformat(),
                package_name=package_name
            )
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{package_name}_{timestamp}_report.html"
            output_path = os.path.join(output_dir, filename)
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 写入HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # 复制静态资源文件
            self._copy_static_resources(output_dir)
            
            self.logger.info(f"HTML报告已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成HTML报告失败: {str(e)}")
            raise
    
    def _copy_static_resources(self, output_dir: str):
        """复制静态资源文件到输出目录"""
        try:
            import shutil
            
            static_dirs = ['css', 'js']
            
            for static_dir in static_dirs:
                src_dir = os.path.join(self.template_dir, static_dir)
                dst_dir = os.path.join(output_dir, static_dir)
                
                if os.path.exists(src_dir):
                    if os.path.exists(dst_dir):
                        shutil.rmtree(dst_dir)
                    shutil.copytree(src_dir, dst_dir)
                    
        except Exception as e:
            self.logger.warning(f"复制静态资源失败: {str(e)}")
    
    def generate_batch_summary_report(self, batch_results: List[Dict[str, Any]], 
                                    output_dir: str) -> str:
        """
        生成批量分析汇总报告
        
        Args:
            batch_results: 批量分析结果列表
            output_dir: 输出目录
            
        Returns:
            生成的汇总报告文件路径
        """
        try:
            # 统计分析结果
            summary_data = self._calculate_batch_summary(batch_results)
            
            # 加载汇总模板
            template = self.jinja_env.get_template('batch_summary_template.html')
            
            # 渲染HTML内容
            html_content = template.render(
                summary_data=summary_data,
                batch_results=batch_results,
                generation_time=datetime.now().isoformat()
            )
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_summary_{timestamp}.html"
            output_path = os.path.join(output_dir, filename)
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 写入HTML文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"批量汇总报告已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"生成批量汇总报告失败: {str(e)}")
            raise
    
    def _calculate_batch_summary(self, batch_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算批量分析汇总统计"""
        total_count = len(batch_results)
        successful_results = [r for r in batch_results if r.get('success', False)]
        failed_results = [r for r in batch_results if not r.get('success', False)]
        
        if successful_results:
            risk_scores = [r.get('risk_score', 0) for r in successful_results]
            avg_risk_score = sum(risk_scores) / len(risk_scores)
            max_risk_score = max(risk_scores)
            min_risk_score = min(risk_scores)
            
            # 风险等级分布
            high_risk = len([s for s in risk_scores if s >= 8.0])
            medium_high_risk = len([s for s in risk_scores if 6.0 <= s < 8.0])
            medium_risk = len([s for s in risk_scores if 4.0 <= s < 6.0])
            low_risk = len([s for s in risk_scores if s < 4.0])
        else:
            avg_risk_score = max_risk_score = min_risk_score = 0
            high_risk = medium_high_risk = medium_risk = low_risk = 0
        
        return {
            'total_count': total_count,
            'successful_count': len(successful_results),
            'failed_count': len(failed_results),
            'success_rate': len(successful_results) / total_count * 100 if total_count > 0 else 0,
            'avg_risk_score': avg_risk_score,
            'max_risk_score': max_risk_score,
            'min_risk_score': min_risk_score,
            'risk_distribution': {
                'high_risk': high_risk,
                'medium_high_risk': medium_high_risk,
                'medium_risk': medium_risk,
                'low_risk': low_risk
            },
            'failed_files': [r.get('apk_path', '') for r in failed_results]
        }
    
    def export_to_csv(self, report_data: Dict[str, Any], output_path: str):
        """
        导出报告数据为CSV格式
        
        Args:
            report_data: 报告数据
            output_path: 输出文件路径
        """
        try:
            import csv
            
            # 提取关键数据
            apk_info = report_data.get('apk_info', {})
            risk_assessment = report_data.get('risk_assessment', {})
            features = report_data.get('features', {})
            
            # 准备CSV数据
            csv_data = {
                'Package Name': apk_info.get('package_name', ''),
                'Version Name': apk_info.get('version_name', ''),
                'Version Code': apk_info.get('version_code', ''),
                'File Size': apk_info.get('file_size', 0),
                'Target SDK': apk_info.get('target_sdk_version', 0),
                'Overall Risk Score': risk_assessment.get('overall_risk_score', 0),
                'Certificate Risk': risk_assessment.get('certificate_risk_score', 0),
                'Permission Risk': risk_assessment.get('permission_risk_score', 0),
                'Code Risk': risk_assessment.get('code_risk_score', 0),
                'Network Risk': risk_assessment.get('network_risk_score', 0),
                'Permissions Count': len(features.get('manifest_features', {}).get('permissions', [])),
                'Components Count': len(features.get('manifest_features', {}).get('activities', [])) + 
                                 len(features.get('manifest_features', {}).get('services', [])),
                'Native Libraries Count': len(features.get('native_features', {}).get('libraries', [])),
                'Network Features Count': len(features.get('network_features', {}).get('domains', [])),
                'Analysis Date': features.get('analysis_date', '')
            }
            
            # 写入CSV文件
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=csv_data.keys())
                writer.writeheader()
                writer.writerow(csv_data)
            
            self.logger.info(f"CSV报告已导出: {output_path}")
            
        except Exception as e:
            self.logger.error(f"导出CSV报告失败: {str(e)}")
            raise
