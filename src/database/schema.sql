-- Android APP Analytics Database Schema
-- SQLite数据库建表语句

-- 1. APK基础信息表
CREATE TABLE IF NOT EXISTS apk_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_md5 TEXT NOT NULL UNIQUE,
    file_sha1 TEXT NOT NULL,
    file_sha256 TEXT NOT NULL,
    package_name TEXT NOT NULL,
    version_name TEXT,
    version_code INTEGER,
    target_sdk_version INTEGER,
    min_sdk_version INTEGER,
    compile_sdk_version INTEGER,
    analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analysis_version TEXT,
    INDEX(package_name),
    INDEX(file_md5),
    INDEX(file_sha256)
);

-- 2. 证书信息表
CREATE TABLE IF NOT EXISTS certificates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    cert_md5 TEXT NOT NULL,
    cert_sha1 TEXT NOT NULL,
    cert_sha256 TEXT NOT NULL,
    subject_dn TEXT,
    issuer_dn TEXT,
    serial_number TEXT,
    not_before TIMESTAMP,
    not_after TIMESTAMP,
    signature_algorithm TEXT,
    is_self_signed BOOLEAN DEFAULT 0,
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(cert_md5),
    INDEX(cert_sha1),
    INDEX(cert_sha256)
);

-- 3. 权限信息表
CREATE TABLE IF NOT EXISTS permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    permission_name TEXT NOT NULL,
    permission_level TEXT, -- normal, dangerous, signature, signatureOrSystem
    is_custom BOOLEAN DEFAULT 0,
    protection_level TEXT,
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(permission_name),
    INDEX(apk_id)
);

-- 4. 组件信息表（Activities, Services, Receivers, Providers）
CREATE TABLE IF NOT EXISTS components (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    component_type TEXT NOT NULL, -- activity, service, receiver, provider
    component_name TEXT NOT NULL,
    is_exported BOOLEAN DEFAULT 0,
    required_permission TEXT,
    intent_filters TEXT, -- JSON格式存储
    attributes TEXT, -- JSON格式存储其他属性
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(component_type),
    INDEX(is_exported),
    INDEX(apk_id)
);

-- 5. 代码特征表
CREATE TABLE IF NOT EXISTS code_features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    class_count INTEGER DEFAULT 0,
    method_count INTEGER DEFAULT 0,
    string_count INTEGER DEFAULT 0,
    api_calls TEXT, -- JSON格式存储API调用统计
    class_names_hash TEXT, -- 类名列表的哈希值
    method_names_hash TEXT, -- 方法名列表的哈希值
    strings_hash TEXT, -- 字符串常量的哈希值
    call_graph_hash TEXT, -- 调用图的哈希值
    obfuscation_score REAL DEFAULT 0.0, -- 混淆程度评分
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(apk_id)
);

-- 6. 资源特征表
CREATE TABLE IF NOT EXISTS resource_features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    icon_hash TEXT, -- 应用图标的感知哈希
    icon_md5 TEXT, -- 应用图标的MD5
    layout_files_hash TEXT, -- 布局文件的哈希
    string_resources_hash TEXT, -- 字符串资源的哈希
    drawable_count INTEGER DEFAULT 0,
    layout_count INTEGER DEFAULT 0,
    raw_count INTEGER DEFAULT 0,
    assets_count INTEGER DEFAULT 0,
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(apk_id),
    INDEX(icon_hash)
);

-- 7. 原生库信息表
CREATE TABLE IF NOT EXISTS native_libraries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    library_name TEXT NOT NULL,
    architecture TEXT NOT NULL, -- arm64-v8a, armeabi-v7a, x86, x86_64
    file_size INTEGER,
    file_md5 TEXT,
    elf_header_info TEXT, -- JSON格式存储ELF头信息
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(apk_id),
    INDEX(architecture),
    INDEX(file_md5)
);

-- 8. 网络特征表
CREATE TABLE IF NOT EXISTS network_features (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    domain_name TEXT NOT NULL,
    url_pattern TEXT,
    protocol TEXT, -- http, https, ftp, etc.
    context TEXT, -- 发现该URL的代码上下文
    confidence_score REAL DEFAULT 1.0, -- 置信度评分
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(apk_id),
    INDEX(domain_name)
);

-- 9. 恶意软件特征库
CREATE TABLE IF NOT EXISTS malware_signatures (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    signature_type TEXT NOT NULL, -- cert, domain, code_hash, pattern
    signature_value TEXT NOT NULL,
    malware_family TEXT,
    severity_level INTEGER DEFAULT 1, -- 1-5, 5为最高风险
    description TEXT,
    source TEXT, -- 特征来源
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1,
    INDEX(signature_type),
    INDEX(signature_value),
    INDEX(malware_family),
    INDEX(severity_level)
);

-- 10. 风险评估结果表
CREATE TABLE IF NOT EXISTS risk_assessments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    apk_id INTEGER NOT NULL,
    overall_risk_score REAL NOT NULL,
    certificate_risk_score REAL DEFAULT 0.0,
    permission_risk_score REAL DEFAULT 0.0,
    component_risk_score REAL DEFAULT 0.0,
    code_risk_score REAL DEFAULT 0.0,
    network_risk_score REAL DEFAULT 0.0,
    similarity_matches TEXT, -- JSON格式存储相似度匹配结果
    risk_factors TEXT, -- JSON格式存储风险因子详情
    assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (apk_id) REFERENCES apk_info(id) ON DELETE CASCADE,
    INDEX(apk_id),
    INDEX(overall_risk_score)
);

-- 11. 分析任务表（用于批量处理和任务队列）
CREATE TABLE IF NOT EXISTS analysis_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL UNIQUE,
    apk_file_path TEXT NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, running, completed, failed
    priority INTEGER DEFAULT 1,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_date TIMESTAMP,
    completed_date TIMESTAMP,
    error_message TEXT,
    result_apk_id INTEGER,
    FOREIGN KEY (result_apk_id) REFERENCES apk_info(id),
    INDEX(task_id),
    INDEX(status),
    INDEX(priority)
);

-- 创建视图：APK完整信息视图
CREATE VIEW IF NOT EXISTS apk_complete_info AS
SELECT 
    ai.*,
    ra.overall_risk_score,
    ra.assessment_date as risk_assessment_date,
    COUNT(DISTINCT p.id) as permission_count,
    COUNT(DISTINCT c.id) as component_count,
    COUNT(DISTINCT nl.id) as native_library_count,
    COUNT(DISTINCT nf.id) as network_feature_count
FROM apk_info ai
LEFT JOIN risk_assessments ra ON ai.id = ra.apk_id
LEFT JOIN permissions p ON ai.id = p.apk_id
LEFT JOIN components c ON ai.id = c.apk_id
LEFT JOIN native_libraries nl ON ai.id = nl.apk_id
LEFT JOIN network_features nf ON ai.id = nf.apk_id
GROUP BY ai.id;

-- 创建触发器：更新时间戳
CREATE TRIGGER IF NOT EXISTS update_malware_signatures_timestamp 
    AFTER UPDATE ON malware_signatures
BEGIN
    UPDATE malware_signatures SET updated_date = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
