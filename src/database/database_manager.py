"""
数据库管理器 - 负责与SQLite特征数据库的交互
"""

import sqlite3
import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from contextlib import contextmanager

from .models import APKInfo, Certificate, Permission, Component, CodeFeature, ResourceFeature
from ..utils.hash_utils import HashUtils


class DatabaseManager:
    """SQLite数据库管理器"""
    
    def __init__(self, database_path: str):
        """
        初始化数据库管理器
        
        Args:
            database_path: 数据库文件路径
        """
        self.database_path = database_path
        self.logger = logging.getLogger(__name__)
        self.hash_utils = HashUtils()
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(database_path), exist_ok=True)
        
        # 初始化数据库
        self._initialize_database()
        
        self.logger.info(f"数据库管理器初始化完成: {database_path}")
    
    def _initialize_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                # 读取并执行schema.sql
                schema_path = os.path.join(os.path.dirname(__file__), 'schema.sql')
                if os.path.exists(schema_path):
                    with open(schema_path, 'r', encoding='utf-8') as f:
                        schema_sql = f.read()
                    conn.executescript(schema_sql)
                    self.logger.info("数据库表结构初始化完成")
                else:
                    self.logger.warning(f"未找到schema文件: {schema_path}")
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.database_path)
            conn.row_factory = sqlite3.Row  # 启用字典式访问
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作错误: {str(e)}")
            raise
        finally:
            if conn:
                conn.close()
    
    def store_apk_analysis(self, apk_info: Dict[str, Any], features: Dict[str, Any]) -> int:
        """
        存储APK分析结果到数据库
        
        Args:
            apk_info: APK基础信息
            features: 提取的特征数据
            
        Returns:
            插入的APK记录ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 插入APK基础信息
                apk_id = self._insert_apk_info(cursor, apk_info, features)
                
                # 2. 插入证书信息
                if 'certificate_features' in features:
                    self._insert_certificates(cursor, apk_id, features['certificate_features'])
                
                # 3. 插入权限信息
                if 'manifest_features' in features and 'permissions' in features['manifest_features']:
                    self._insert_permissions(cursor, apk_id, features['manifest_features']['permissions'])
                
                # 4. 插入组件信息
                if 'manifest_features' in features:
                    self._insert_components(cursor, apk_id, features['manifest_features'])
                
                # 5. 插入代码特征
                if 'code_features' in features:
                    self._insert_code_features(cursor, apk_id, features['code_features'])
                
                # 6. 插入资源特征
                if 'resource_features' in features:
                    self._insert_resource_features(cursor, apk_id, features['resource_features'])
                
                # 7. 插入原生库信息
                if 'native_features' in features and 'libraries' in features['native_features']:
                    self._insert_native_libraries(cursor, apk_id, features['native_features']['libraries'])
                
                # 8. 插入网络特征
                if 'network_features' in features and 'domains' in features['network_features']:
                    self._insert_network_features(cursor, apk_id, features['network_features']['domains'])
                
                conn.commit()
                self.logger.info(f"APK分析结果已存储，ID: {apk_id}")
                return apk_id
                
        except Exception as e:
            self.logger.error(f"存储APK分析结果失败: {str(e)}")
            raise
    
    def _insert_apk_info(self, cursor, apk_info: Dict[str, Any], features: Dict[str, Any]) -> int:
        """插入APK基础信息"""
        sql = """
        INSERT INTO apk_info (
            file_path, file_name, file_size, file_md5, file_sha1, file_sha256,
            package_name, version_name, version_code, target_sdk_version,
            min_sdk_version, compile_sdk_version, analysis_date, analysis_version
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        values = (
            apk_info.get('file_path', ''),
            apk_info.get('file_name', ''),
            apk_info.get('file_size', 0),
            apk_info.get('file_md5', ''),
            apk_info.get('file_sha1', ''),
            apk_info.get('file_sha256', ''),
            apk_info.get('package_name', ''),
            apk_info.get('version_name', ''),
            apk_info.get('version_code', 0),
            apk_info.get('target_sdk_version', 0),
            apk_info.get('min_sdk_version', 0),
            apk_info.get('compile_sdk_version', 0),
            features.get('analysis_date', datetime.now().isoformat()),
            '1.0.0'  # 工具版本
        )
        
        cursor.execute(sql, values)
        return cursor.lastrowid
    
    def _insert_certificates(self, cursor, apk_id: int, cert_features: Dict[str, Any]):
        """插入证书信息"""
        if not cert_features:
            return
        
        sql = """
        INSERT INTO certificates (
            apk_id, cert_md5, cert_sha1, cert_sha256, subject_dn, issuer_dn,
            serial_number, not_before, not_after, signature_algorithm, is_self_signed
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        values = (
            apk_id,
            cert_features.get('cert_md5', ''),
            cert_features.get('cert_sha1', ''),
            cert_features.get('cert_sha256', ''),
            cert_features.get('subject_dn', ''),
            cert_features.get('issuer_dn', ''),
            cert_features.get('serial_number', ''),
            cert_features.get('not_before', ''),
            cert_features.get('not_after', ''),
            cert_features.get('signature_algorithm', ''),
            cert_features.get('is_self_signed', False)
        )
        
        cursor.execute(sql, values)
    
    def _insert_permissions(self, cursor, apk_id: int, permissions: List[Dict[str, Any]]):
        """插入权限信息"""
        if not permissions:
            return
        
        sql = """
        INSERT INTO permissions (apk_id, permission_name, permission_level, is_custom, protection_level)
        VALUES (?, ?, ?, ?, ?)
        """
        
        for perm in permissions:
            values = (
                apk_id,
                perm.get('name', ''),
                perm.get('level', ''),
                perm.get('is_custom', False),
                perm.get('protection_level', '')
            )
            cursor.execute(sql, values)
    
    def _insert_components(self, cursor, apk_id: int, manifest_features: Dict[str, Any]):
        """插入组件信息"""
        component_types = ['activities', 'services', 'receivers', 'providers']
        
        sql = """
        INSERT INTO components (
            apk_id, component_type, component_name, is_exported, 
            required_permission, intent_filters, attributes
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        
        for comp_type in component_types:
            components = manifest_features.get(comp_type, [])
            for comp in components:
                values = (
                    apk_id,
                    comp_type[:-1],  # 去掉复数形式
                    comp.get('name', ''),
                    comp.get('exported', False),
                    comp.get('permission', ''),
                    json.dumps(comp.get('intent_filters', [])),
                    json.dumps(comp.get('attributes', {}))
                )
                cursor.execute(sql, values)
    
    def _insert_code_features(self, cursor, apk_id: int, code_features: Dict[str, Any]):
        """插入代码特征"""
        sql = """
        INSERT INTO code_features (
            apk_id, class_count, method_count, string_count, api_calls,
            class_names_hash, method_names_hash, strings_hash, call_graph_hash, obfuscation_score
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        values = (
            apk_id,
            code_features.get('class_count', 0),
            code_features.get('method_count', 0),
            code_features.get('string_count', 0),
            json.dumps(code_features.get('api_calls', {})),
            code_features.get('class_names_hash', ''),
            code_features.get('method_names_hash', ''),
            code_features.get('strings_hash', ''),
            code_features.get('call_graph_hash', ''),
            code_features.get('obfuscation_score', 0.0)
        )
        
        cursor.execute(sql, values)
    
    def _insert_resource_features(self, cursor, apk_id: int, resource_features: Dict[str, Any]):
        """插入资源特征"""
        sql = """
        INSERT INTO resource_features (
            apk_id, icon_hash, icon_md5, layout_files_hash, string_resources_hash,
            drawable_count, layout_count, raw_count, assets_count
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        values = (
            apk_id,
            resource_features.get('icon_hash', ''),
            resource_features.get('icon_md5', ''),
            resource_features.get('layout_files_hash', ''),
            resource_features.get('string_resources_hash', ''),
            resource_features.get('drawable_count', 0),
            resource_features.get('layout_count', 0),
            resource_features.get('raw_count', 0),
            resource_features.get('assets_count', 0)
        )
        
        cursor.execute(sql, values)
    
    def _insert_native_libraries(self, cursor, apk_id: int, libraries: List[Dict[str, Any]]):
        """插入原生库信息"""
        if not libraries:
            return
        
        sql = """
        INSERT INTO native_libraries (
            apk_id, library_name, architecture, file_size, file_md5, elf_header_info
        ) VALUES (?, ?, ?, ?, ?, ?)
        """
        
        for lib in libraries:
            values = (
                apk_id,
                lib.get('name', ''),
                lib.get('architecture', ''),
                lib.get('size', 0),
                lib.get('md5', ''),
                json.dumps(lib.get('elf_info', {}))
            )
            cursor.execute(sql, values)
    
    def _insert_network_features(self, cursor, apk_id: int, domains: List[Dict[str, Any]]):
        """插入网络特征"""
        if not domains:
            return
        
        sql = """
        INSERT INTO network_features (
            apk_id, domain_name, url_pattern, protocol, context, confidence_score
        ) VALUES (?, ?, ?, ?, ?, ?)
        """
        
        for domain in domains:
            values = (
                apk_id,
                domain.get('domain', ''),
                domain.get('url_pattern', ''),
                domain.get('protocol', ''),
                domain.get('context', ''),
                domain.get('confidence', 1.0)
            )
            cursor.execute(sql, values)
    
    def store_risk_assessment(self, apk_id: int, risk_assessment: Dict[str, Any]):
        """存储风险评估结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = """
                INSERT INTO risk_assessments (
                    apk_id, overall_risk_score, certificate_risk_score, permission_risk_score,
                    component_risk_score, code_risk_score, network_risk_score,
                    similarity_matches, risk_factors
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                values = (
                    apk_id,
                    risk_assessment.get('overall_risk_score', 0.0),
                    risk_assessment.get('certificate_risk_score', 0.0),
                    risk_assessment.get('permission_risk_score', 0.0),
                    risk_assessment.get('component_risk_score', 0.0),
                    risk_assessment.get('code_risk_score', 0.0),
                    risk_assessment.get('network_risk_score', 0.0),
                    json.dumps(risk_assessment.get('similarity_matches', [])),
                    json.dumps(risk_assessment.get('risk_factors', {}))
                )
                
                cursor.execute(sql, values)
                conn.commit()
                
                self.logger.info(f"风险评估结果已存储，APK ID: {apk_id}")
                
        except Exception as e:
            self.logger.error(f"存储风险评估结果失败: {str(e)}")
            raise
    
    def find_similar_apks(self, features: Dict[str, Any], similarity_threshold: float = 0.8) -> List[Dict[str, Any]]:
        """
        查找相似的APK
        
        Args:
            features: 待比较的特征
            similarity_threshold: 相似度阈值
            
        Returns:
            相似APK列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 基于证书哈希查找
                cert_matches = self._find_by_certificate(cursor, features.get('certificate_features', {}))
                
                # 基于代码特征查找
                code_matches = self._find_by_code_features(cursor, features.get('code_features', {}))
                
                # 基于网络特征查找
                network_matches = self._find_by_network_features(cursor, features.get('network_features', {}))
                
                # 合并并去重
                all_matches = cert_matches + code_matches + network_matches
                unique_matches = {match['apk_id']: match for match in all_matches}.values()
                
                return list(unique_matches)
                
        except Exception as e:
            self.logger.error(f"查找相似APK失败: {str(e)}")
            return []
    
    def _find_by_certificate(self, cursor, cert_features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于证书查找相似APK"""
        if not cert_features:
            return []
        
        sql = """
        SELECT ai.*, c.cert_sha256, 'certificate' as match_type, 5.0 as risk_score
        FROM apk_info ai
        JOIN certificates c ON ai.id = c.apk_id
        WHERE c.cert_sha256 = ?
        """
        
        cursor.execute(sql, (cert_features.get('cert_sha256', ''),))
        return [dict(row) for row in cursor.fetchall()]
    
    def get_malware_signatures(self) -> List[Dict[str, Any]]:
        """获取恶意软件特征库"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = """
                SELECT * FROM malware_signatures 
                WHERE is_active = 1 
                ORDER BY severity_level DESC, created_date DESC
                """
                
                cursor.execute(sql)
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"获取恶意软件特征库失败: {str(e)}")
            return []
